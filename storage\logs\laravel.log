[2025-07-31 05:50:29] local.ERROR: Call to a member function getKey() on bool {"userId":1,"exception":"[object] (Error(code: 0): Call to a member function getKey() on bool at C:\\xampp\\htdocs\\bulkmail\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Collection.php:555)
[stacktrace]
#0 C:\\xampp\\htdocs\\bulkmail\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Collection.php(426): Illuminate\\Database\\Eloquent\\Collection->getDictionary(Object(Spatie\\Permission\\Models\\Role))
#1 C:\\xampp\\htdocs\\bulkmail\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php(292): Illuminate\\Database\\Eloquent\\Collection->intersect(Object(Spatie\\Permission\\Models\\Role))
#2 C:\\xampp\\htdocs\\bulkmail\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php(314): App\\Models\\User->hasRole(Object(Illuminate\\Database\\Eloquent\\Collection))
#3 C:\\xampp\\htdocs\\bulkmail\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php(217): App\\Models\\User->hasPermissionViaRole(Object(Spatie\\Permission\\Models\\Permission))
#4 C:\\xampp\\htdocs\\bulkmail\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php(263): App\\Models\\User->hasPermissionTo(Object(Spatie\\Permission\\Models\\Permission), NULL)
#5 C:\\xampp\\htdocs\\bulkmail\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php(128): App\\Models\\User->checkPermissionTo('List Setting', NULL)
#6 C:\\xampp\\htdocs\\bulkmail\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php(566): Spatie\\Permission\\PermissionRegistrar->Spatie\\Permission\\{closure}(Object(App\\Models\\User), 'List Setting', Array)
#7 C:\\xampp\\htdocs\\bulkmail\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php(439): Illuminate\\Auth\\Access\\Gate->callBeforeCallbacks(Object(App\\Models\\User), 'List Setting', Array)
#8 C:\\xampp\\htdocs\\bulkmail\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php(406): Illuminate\\Auth\\Access\\Gate->raw('List Setting', Array)
#9 C:\\xampp\\htdocs\\bulkmail\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php(354): Illuminate\\Auth\\Access\\Gate->inspect('List Setting', Array)
#10 C:\\xampp\\htdocs\\bulkmail\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Traits\\EnumeratesValues.php(277): Illuminate\\Auth\\Access\\Gate->Illuminate\\Auth\\Access\\{closure}('List Setting', 0)
#11 C:\\xampp\\htdocs\\bulkmail\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php(355): Illuminate\\Support\\Collection->every(Object(Closure))
#12 C:\\xampp\\htdocs\\bulkmail\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php(367): Illuminate\\Auth\\Access\\Gate->check('List Setting', Array)
#13 C:\\xampp\\htdocs\\bulkmail\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Arr.php(203): Illuminate\\Auth\\Access\\Gate->Illuminate\\Auth\\Access\\{closure}('List Setting', 0)
#14 C:\\xampp\\htdocs\\bulkmail\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Collection.php(405): Illuminate\\Support\\Arr::first(Array, Object(Closure), Object(stdClass))
#15 C:\\xampp\\htdocs\\bulkmail\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Collection.php(177): Illuminate\\Support\\Collection->first(Object(Closure), Object(stdClass))
#16 C:\\xampp\\htdocs\\bulkmail\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php(367): Illuminate\\Support\\Collection->contains(Object(Closure))
#17 C:\\xampp\\htdocs\\bulkmail\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Auth\\Access\\Authorizable.php(30): Illuminate\\Auth\\Access\\Gate->any(Array, Array)
#18 C:\\xampp\\htdocs\\bulkmail\\vendor\\spatie\\laravel-permission\\src\\Middleware\\PermissionMiddleware.php(33): Illuminate\\Foundation\\Auth\\User->canAny(Array)
#19 C:\\xampp\\htdocs\\bulkmail\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Spatie\\Permission\\Middleware\\PermissionMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'List Setting')
#20 C:\\xampp\\htdocs\\bulkmail\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets.php(20): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 C:\\xampp\\htdocs\\bulkmail\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 C:\\xampp\\htdocs\\bulkmail\\vendor\\inertiajs\\inertia-laravel\\src\\Middleware.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\xampp\\htdocs\\bulkmail\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Inertia\\Middleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\xampp\\htdocs\\bulkmail\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\xampp\\htdocs\\bulkmail\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\xampp\\htdocs\\bulkmail\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 C:\\xampp\\htdocs\\bulkmail\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\xampp\\htdocs\\bulkmail\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\xampp\\htdocs\\bulkmail\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\xampp\\htdocs\\bulkmail\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\xampp\\htdocs\\bulkmail\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\xampp\\htdocs\\bulkmail\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\xampp\\htdocs\\bulkmail\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#34 C:\\xampp\\htdocs\\bulkmail\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\xampp\\htdocs\\bulkmail\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\xampp\\htdocs\\bulkmail\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 C:\\xampp\\htdocs\\bulkmail\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\xampp\\htdocs\\bulkmail\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 C:\\xampp\\htdocs\\bulkmail\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 C:\\xampp\\htdocs\\bulkmail\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#41 C:\\xampp\\htdocs\\bulkmail\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#42 C:\\xampp\\htdocs\\bulkmail\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#43 C:\\xampp\\htdocs\\bulkmail\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#44 C:\\xampp\\htdocs\\bulkmail\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#45 C:\\xampp\\htdocs\\bulkmail\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#46 C:\\xampp\\htdocs\\bulkmail\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 C:\\xampp\\htdocs\\bulkmail\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 C:\\xampp\\htdocs\\bulkmail\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 C:\\xampp\\htdocs\\bulkmail\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#50 C:\\xampp\\htdocs\\bulkmail\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 C:\\xampp\\htdocs\\bulkmail\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 C:\\xampp\\htdocs\\bulkmail\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#53 C:\\xampp\\htdocs\\bulkmail\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 C:\\xampp\\htdocs\\bulkmail\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#55 C:\\xampp\\htdocs\\bulkmail\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#56 C:\\xampp\\htdocs\\bulkmail\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#57 C:\\xampp\\htdocs\\bulkmail\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#58 C:\\xampp\\htdocs\\bulkmail\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#59 C:\\xampp\\htdocs\\bulkmail\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#60 C:\\xampp\\htdocs\\bulkmail\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#61 C:\\xampp\\htdocs\\bulkmail\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#62 C:\\xampp\\htdocs\\bulkmail\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#63 C:\\xampp\\htdocs\\bulkmail\\public\\index.php(52): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#64 C:\\xampp\\htdocs\\bulkmail\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(16): require_once('C:\\\\xampp\\\\htdocs...')
#65 {main}
"} 
