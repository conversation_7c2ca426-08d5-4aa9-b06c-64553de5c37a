import{T as v,o as u,c as p,a as o,u as t,w as n,F as g,Z as x,b as e,g as _,h as w,k as h,y as V,d as j,G as k,t as P}from"./app-f4e029a5.js";import{_ as U,b as $}from"./AdminLayout-f619cb63.js";import{_ as a}from"./InputLabel-0f849d64.js";import{_ as m}from"./TextInput-8aed9252.js";import{_ as d}from"./InputError-00daa197.js";import{P as D}from"./PrimaryButton-faa68167.js";import{_ as E}from"./CreateButton-4e2a5433.js";import"./_plugin-vue_export-helper-c27b6911.js";const B={class:"items-start"},N={class:"flex justify-between items-center mb-6"},S=e("h1",{class:"text-2xl font-semibold leading-7 text-gray-900"},"Edit Portfolio Project",-1),T={class:"flex space-x-2"},L={class:"bg-white rounded-lg shadow p-6"},M=["onSubmit"],A={class:"grid grid-cols-1 md:grid-cols-2 gap-4"},C={class:"mt-2 grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3 max-h-60 overflow-y-auto border border-gray-300 rounded-md p-4"},F=["id","value"],I=["for"],q=e("p",{class:"mt-1 text-sm text-gray-500"},"Select all technologies used in this project",-1),G={class:"flex mt-6 items-center justify-between"},R={class:"ml-auto flex items-center justify-end gap-x-6"},Z=e("button",{type:"button",class:"text-sm font-semibold leading-6 text-gray-900"},"Back",-1),Y={__name:"Edit",props:["portfolio","technologies"],setup(c){const i=c,s=v({project_name:i.portfolio.project_name,url:i.portfolio.url||"",description:i.portfolio.description||"",technology:Array.isArray(i.portfolio.technology)?i.portfolio.technology:[],login_id:i.portfolio.login_id||"",password:i.portfolio.password||""}),y=()=>{s.patch(route("portfolios.update",i.portfolio.id))};return(f,r)=>(u(),p(g,null,[o(t(x),{title:"Portfolio"}),o(U,null,{default:n(()=>[e("div",B,[e("div",N,[S,e("div",T,[o(E,{href:f.route("portfolios.show",c.portfolio.id)},{default:n(()=>[_(" View Project ")]),_:1},8,["href"])])]),e("div",L,[e("form",{onSubmit:w(y,["prevent"]),class:"space-y-6"},[e("div",A,[e("div",null,[o(a,{for:"project_name",value:"Project Name *"}),o(m,{id:"project_name",modelValue:t(s).project_name,"onUpdate:modelValue":r[0]||(r[0]=l=>t(s).project_name=l),type:"text",class:"mt-1 block w-full",required:"",autofocus:"",placeholder:"Enter project name"},null,8,["modelValue"]),o(d,{class:"mt-2",message:t(s).errors.project_name},null,8,["message"])]),e("div",null,[o(a,{for:"url",value:"Project URL"}),o(m,{id:"url",modelValue:t(s).url,"onUpdate:modelValue":r[1]||(r[1]=l=>t(s).url=l),type:"url",class:"mt-1 block w-full",placeholder:"https://example.com"},null,8,["modelValue"]),o(d,{class:"mt-2",message:t(s).errors.url},null,8,["message"])]),e("div",null,[o(a,{for:"description",value:"Description"}),h(e("textarea",{id:"description","onUpdate:modelValue":r[2]||(r[2]=l=>t(s).description=l),rows:"3",class:"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:border-indigo-500 focus:ring-indigo-500",placeholder:"Describe your project..."},null,512),[[V,t(s).description]]),o(d,{class:"mt-2",message:t(s).errors.description},null,8,["message"])]),e("div",null,[o(a,{for:"technology",value:"Technologies Used"}),e("div",C,[(u(!0),p(g,null,j(c.technologies,l=>(u(),p("div",{key:l.id,class:"flex items-center"},[h(e("input",{id:`tech-${l.id}`,"onUpdate:modelValue":r[3]||(r[3]=b=>t(s).technology=b),value:l.id,type:"checkbox",class:"h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"},null,8,F),[[k,t(s).technology]]),e("label",{for:`tech-${l.id}`,class:"ml-2 text-sm text-gray-700"},P(l.name),9,I)]))),128))]),q,o(d,{class:"mt-2",message:t(s).errors.technology},null,8,["message"])]),e("div",null,[o(a,{for:"login_id",value:"Login ID"}),o(m,{id:"login_id",modelValue:t(s).login_id,"onUpdate:modelValue":r[4]||(r[4]=l=>t(s).login_id=l),type:"text",class:"mt-1 block w-full",placeholder:"Enter login ID (optional)"},null,8,["modelValue"]),o(d,{class:"mt-2",message:t(s).errors.login_id},null,8,["message"])]),e("div",null,[o(a,{for:"password",value:"Password"}),o(m,{id:"password",modelValue:t(s).password,"onUpdate:modelValue":r[5]||(r[5]=l=>t(s).password=l),type:"text",class:"mt-1 block w-full",placeholder:"Enter password (optional)"},null,8,["modelValue"]),o(d,{class:"mt-2",message:t(s).errors.password},null,8,["message"])])]),e("div",G,[e("div",R,[o($,{href:f.route("portfolios.index")},{svg:n(()=>[Z]),_:1},8,["href"]),o(D,{disabled:t(s).processing},{default:n(()=>[_("Update")]),_:1},8,["disabled"])])])],40,M)])])]),_:1})],64))}};export{Y as default};
