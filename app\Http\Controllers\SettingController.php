<?php

namespace App\Http\Controllers;
use App\Models\User;
use Illuminate\Support\Facades\DB;
use Inertia\Inertia;
use Illuminate\Http\Request;
use App\Models\Setting;
use Illuminate\Support\Facades\Redirect;
use Illuminate\Support\Facades\Auth;
use Config;
use Spatie\Activitylog\Models\Activity;
use App\Exports\StockExport;
use App\Models\PurchaseOrderReceiveDetails;
use Maatwebsite\Excel\Facades\Excel;


class SettingController extends Controller
{
    public function __construct()
    {
        $this->middleware('permission:List Setting')->only(['index']);
    }

    public function index(Request $request)
    {
        $permissions = [
            'canPermissionsAdd'   => auth()->user()->can('Roles & Permissions'),
        ];
        return Inertia::render('Settings/Index', compact('permissions'));
    }





}
