import{q as u,k as d,G as n,o as l,c as i}from"./app-f4e029a5.js";const p=["value"],h={__name:"Checkbox",props:{checked:{type:[<PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON>],required:!0},value:{default:null}},emits:["update:checked"],setup(e,{emit:a}){const r=e,t=u({get(){return r.checked},set(o){a("update:checked",o)}});return(o,c)=>d((l(),i("input",{type:"checkbox",value:e.value,"onUpdate:modelValue":c[0]||(c[0]=s=>t.value=s),class:"rounded border-gray-300 text-indigo-600 shadow-sm focus:ring-indigo-500"},null,8,p)),[[n,t.value]])}};export{h as _};
