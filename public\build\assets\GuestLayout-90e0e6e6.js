import{o as t,c as o,b as e,a,w as r,u as c,j as l,s as n}from"./app-f4e029a5.js";const d={class:"min-h-screen flex flex-col sm:justify-center items-center pt-6 sm:pt-0 bg-gray-100"},i={class:"w-60 fill-current text-gray-500"},_=["src"],m={class:"w-full sm:max-w-md mt-6 p-6 bg-white shadow overflow-hidden sm:rounded-lg border-gray-300"},p={__name:"GuestLayout",setup(u){return(s,f)=>(t(),o("div",d,[e("div",null,[a(c(l),{href:"/"},{default:r(()=>[e("div",i,[e("img",{src:"/mototivebg.png",alt:"LOGO"},null,8,_)])]),_:1})]),e("div",m,[n(s.$slots,"default")])]))}};export{p as _};
