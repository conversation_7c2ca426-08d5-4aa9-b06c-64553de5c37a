import{R as ye,S as Ce,U as Fe,V as Ue,W as Qe,i as Xe,X as Je,r as Re,x as we,H as je,Y as et}from"./app-f4e029a5.js";var He={exports:{}};/*!
 * Quill Editor v1.3.7
 * https://quilljs.com/
 * Copyright (c) 2014, Jason <PERSON>
 * Copyright (c) 2013, salesforce.com
 */(function(L,B){(function(v,c){L.exports=c()})(typeof self<"u"?self:ye,function(){return function(g){var v={};function c(O){if(v[O])return v[O].exports;var m=v[O]={i:O,l:!1,exports:{}};return g[O].call(m.exports,m,m.exports,c),m.l=!0,m.exports}return c.m=g,c.c=v,c.d=function(O,m,b){c.o(O,m)||Object.defineProperty(O,m,{configurable:!1,enumerable:!0,get:b})},c.n=function(O){var m=O&&O.__esModule?function(){return O.default}:function(){return O};return c.d(m,"a",m),m},c.o=function(O,m){return Object.prototype.hasOwnProperty.call(O,m)},c.p="",c(c.s=109)}([function(g,v,c){Object.defineProperty(v,"__esModule",{value:!0});var O=c(17),m=c(18),b=c(19),p=c(45),y=c(46),s=c(47),o=c(48),e=c(49),t=c(12),u=c(32),l=c(33),a=c(31),r=c(1),i={Scope:r.Scope,create:r.create,find:r.find,query:r.query,register:r.register,Container:O.default,Format:m.default,Leaf:b.default,Embed:o.default,Scroll:p.default,Block:s.default,Inline:y.default,Text:e.default,Attributor:{Attribute:t.default,Class:u.default,Style:l.default,Store:a.default}};v.default=i},function(g,v,c){var O=this&&this.__extends||function(){var a=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(r,i){r.__proto__=i}||function(r,i){for(var f in i)i.hasOwnProperty(f)&&(r[f]=i[f])};return function(r,i){a(r,i);function f(){this.constructor=r}r.prototype=i===null?Object.create(i):(f.prototype=i.prototype,new f)}}();Object.defineProperty(v,"__esModule",{value:!0});var m=function(a){O(r,a);function r(i){var f=this;return i="[Parchment] "+i,f=a.call(this,i)||this,f.message=i,f.name=f.constructor.name,f}return r}(Error);v.ParchmentError=m;var b={},p={},y={},s={};v.DATA_KEY="__blot";var o;(function(a){a[a.TYPE=3]="TYPE",a[a.LEVEL=12]="LEVEL",a[a.ATTRIBUTE=13]="ATTRIBUTE",a[a.BLOT=14]="BLOT",a[a.INLINE=7]="INLINE",a[a.BLOCK=11]="BLOCK",a[a.BLOCK_BLOT=10]="BLOCK_BLOT",a[a.INLINE_BLOT=6]="INLINE_BLOT",a[a.BLOCK_ATTRIBUTE=9]="BLOCK_ATTRIBUTE",a[a.INLINE_ATTRIBUTE=5]="INLINE_ATTRIBUTE",a[a.ANY=15]="ANY"})(o=v.Scope||(v.Scope={}));function e(a,r){var i=u(a);if(i==null)throw new m("Unable to create "+a+" blot");var f=i,n=a instanceof Node||a.nodeType===Node.TEXT_NODE?a:f.create(r);return new f(n,r)}v.create=e;function t(a,r){return r===void 0&&(r=!1),a==null?null:a[v.DATA_KEY]!=null?a[v.DATA_KEY].blot:r?t(a.parentNode,r):null}v.find=t;function u(a,r){r===void 0&&(r=o.ANY);var i;if(typeof a=="string")i=s[a]||b[a];else if(a instanceof Text||a.nodeType===Node.TEXT_NODE)i=s.text;else if(typeof a=="number")a&o.LEVEL&o.BLOCK?i=s.block:a&o.LEVEL&o.INLINE&&(i=s.inline);else if(a instanceof HTMLElement){var f=(a.getAttribute("class")||"").split(/\s+/);for(var n in f)if(i=p[f[n]],i)break;i=i||y[a.tagName]}return i==null?null:r&o.LEVEL&i.scope&&r&o.TYPE&i.scope?i:null}v.query=u;function l(){for(var a=[],r=0;r<arguments.length;r++)a[r]=arguments[r];if(a.length>1)return a.map(function(n){return l(n)});var i=a[0];if(typeof i.blotName!="string"&&typeof i.attrName!="string")throw new m("Invalid definition");if(i.blotName==="abstract")throw new m("Cannot register abstract class");if(s[i.blotName||i.attrName]=i,typeof i.keyName=="string")b[i.keyName]=i;else if(i.className!=null&&(p[i.className]=i),i.tagName!=null){Array.isArray(i.tagName)?i.tagName=i.tagName.map(function(n){return n.toUpperCase()}):i.tagName=i.tagName.toUpperCase();var f=Array.isArray(i.tagName)?i.tagName:[i.tagName];f.forEach(function(n){(y[n]==null||i.className==null)&&(y[n]=i)})}return i}v.register=l},function(g,v,c){var O=c(51),m=c(11),b=c(3),p=c(20),y=String.fromCharCode(0),s=function(o){Array.isArray(o)?this.ops=o:o!=null&&Array.isArray(o.ops)?this.ops=o.ops:this.ops=[]};s.prototype.insert=function(o,e){var t={};return o.length===0?this:(t.insert=o,e!=null&&typeof e=="object"&&Object.keys(e).length>0&&(t.attributes=e),this.push(t))},s.prototype.delete=function(o){return o<=0?this:this.push({delete:o})},s.prototype.retain=function(o,e){if(o<=0)return this;var t={retain:o};return e!=null&&typeof e=="object"&&Object.keys(e).length>0&&(t.attributes=e),this.push(t)},s.prototype.push=function(o){var e=this.ops.length,t=this.ops[e-1];if(o=b(!0,{},o),typeof t=="object"){if(typeof o.delete=="number"&&typeof t.delete=="number")return this.ops[e-1]={delete:t.delete+o.delete},this;if(typeof t.delete=="number"&&o.insert!=null&&(e-=1,t=this.ops[e-1],typeof t!="object"))return this.ops.unshift(o),this;if(m(o.attributes,t.attributes)){if(typeof o.insert=="string"&&typeof t.insert=="string")return this.ops[e-1]={insert:t.insert+o.insert},typeof o.attributes=="object"&&(this.ops[e-1].attributes=o.attributes),this;if(typeof o.retain=="number"&&typeof t.retain=="number")return this.ops[e-1]={retain:t.retain+o.retain},typeof o.attributes=="object"&&(this.ops[e-1].attributes=o.attributes),this}}return e===this.ops.length?this.ops.push(o):this.ops.splice(e,0,o),this},s.prototype.chop=function(){var o=this.ops[this.ops.length-1];return o&&o.retain&&!o.attributes&&this.ops.pop(),this},s.prototype.filter=function(o){return this.ops.filter(o)},s.prototype.forEach=function(o){this.ops.forEach(o)},s.prototype.map=function(o){return this.ops.map(o)},s.prototype.partition=function(o){var e=[],t=[];return this.forEach(function(u){var l=o(u)?e:t;l.push(u)}),[e,t]},s.prototype.reduce=function(o,e){return this.ops.reduce(o,e)},s.prototype.changeLength=function(){return this.reduce(function(o,e){return e.insert?o+p.length(e):e.delete?o-e.delete:o},0)},s.prototype.length=function(){return this.reduce(function(o,e){return o+p.length(e)},0)},s.prototype.slice=function(o,e){o=o||0,typeof e!="number"&&(e=1/0);for(var t=[],u=p.iterator(this.ops),l=0;l<e&&u.hasNext();){var a;l<o?a=u.next(o-l):(a=u.next(e-l),t.push(a)),l+=p.length(a)}return new s(t)},s.prototype.compose=function(o){var e=p.iterator(this.ops),t=p.iterator(o.ops),u=[],l=t.peek();if(l!=null&&typeof l.retain=="number"&&l.attributes==null){for(var a=l.retain;e.peekType()==="insert"&&e.peekLength()<=a;)a-=e.peekLength(),u.push(e.next());l.retain-a>0&&t.next(l.retain-a)}for(var r=new s(u);e.hasNext()||t.hasNext();)if(t.peekType()==="insert")r.push(t.next());else if(e.peekType()==="delete")r.push(e.next());else{var i=Math.min(e.peekLength(),t.peekLength()),f=e.next(i),n=t.next(i);if(typeof n.retain=="number"){var h={};typeof f.retain=="number"?h.retain=i:h.insert=f.insert;var w=p.attributes.compose(f.attributes,n.attributes,typeof f.retain=="number");if(w&&(h.attributes=w),r.push(h),!t.hasNext()&&m(r.ops[r.ops.length-1],h)){var A=new s(e.rest());return r.concat(A).chop()}}else typeof n.delete=="number"&&typeof f.retain=="number"&&r.push(n)}return r.chop()},s.prototype.concat=function(o){var e=new s(this.ops.slice());return o.ops.length>0&&(e.push(o.ops[0]),e.ops=e.ops.concat(o.ops.slice(1))),e},s.prototype.diff=function(o,e){if(this.ops===o.ops)return new s;var t=[this,o].map(function(i){return i.map(function(f){if(f.insert!=null)return typeof f.insert=="string"?f.insert:y;var n=i===o?"on":"with";throw new Error("diff() called "+n+" non-document")}).join("")}),u=new s,l=O(t[0],t[1],e),a=p.iterator(this.ops),r=p.iterator(o.ops);return l.forEach(function(i){for(var f=i[1].length;f>0;){var n=0;switch(i[0]){case O.INSERT:n=Math.min(r.peekLength(),f),u.push(r.next(n));break;case O.DELETE:n=Math.min(f,a.peekLength()),a.next(n),u.delete(n);break;case O.EQUAL:n=Math.min(a.peekLength(),r.peekLength(),f);var h=a.next(n),w=r.next(n);m(h.insert,w.insert)?u.retain(n,p.attributes.diff(h.attributes,w.attributes)):u.push(w).delete(n);break}f-=n}}),u.chop()},s.prototype.eachLine=function(o,e){e=e||`
`;for(var t=p.iterator(this.ops),u=new s,l=0;t.hasNext();){if(t.peekType()!=="insert")return;var a=t.peek(),r=p.length(a)-t.peekLength(),i=typeof a.insert=="string"?a.insert.indexOf(e,r)-r:-1;if(i<0)u.push(t.next());else if(i>0)u.push(t.next(i));else{if(o(u,t.next(1).attributes||{},l)===!1)return;l+=1,u=new s}}u.length()>0&&o(u,{},l)},s.prototype.transform=function(o,e){if(e=!!e,typeof o=="number")return this.transformPosition(o,e);for(var t=p.iterator(this.ops),u=p.iterator(o.ops),l=new s;t.hasNext()||u.hasNext();)if(t.peekType()==="insert"&&(e||u.peekType()!=="insert"))l.retain(p.length(t.next()));else if(u.peekType()==="insert")l.push(u.next());else{var a=Math.min(t.peekLength(),u.peekLength()),r=t.next(a),i=u.next(a);if(r.delete)continue;i.delete?l.push(i):l.retain(a,p.attributes.transform(r.attributes,i.attributes,e))}return l.chop()},s.prototype.transformPosition=function(o,e){e=!!e;for(var t=p.iterator(this.ops),u=0;t.hasNext()&&u<=o;){var l=t.peekLength(),a=t.peekType();if(t.next(),a==="delete"){o-=Math.min(l,o-u);continue}else a==="insert"&&(u<o||!e)&&(o+=l);u+=l}return o},g.exports=s},function(g,v){var c=Object.prototype.hasOwnProperty,O=Object.prototype.toString,m=Object.defineProperty,b=Object.getOwnPropertyDescriptor,p=function(t){return typeof Array.isArray=="function"?Array.isArray(t):O.call(t)==="[object Array]"},y=function(t){if(!t||O.call(t)!=="[object Object]")return!1;var u=c.call(t,"constructor"),l=t.constructor&&t.constructor.prototype&&c.call(t.constructor.prototype,"isPrototypeOf");if(t.constructor&&!u&&!l)return!1;var a;for(a in t);return typeof a>"u"||c.call(t,a)},s=function(t,u){m&&u.name==="__proto__"?m(t,u.name,{enumerable:!0,configurable:!0,value:u.newValue,writable:!0}):t[u.name]=u.newValue},o=function(t,u){if(u==="__proto__")if(c.call(t,u)){if(b)return b(t,u).value}else return;return t[u]};g.exports=function e(){var t,u,l,a,r,i,f=arguments[0],n=1,h=arguments.length,w=!1;for(typeof f=="boolean"&&(w=f,f=arguments[1]||{},n=2),(f==null||typeof f!="object"&&typeof f!="function")&&(f={});n<h;++n)if(t=arguments[n],t!=null)for(u in t)l=o(f,u),a=o(t,u),f!==a&&(w&&a&&(y(a)||(r=p(a)))?(r?(r=!1,i=l&&p(l)?l:[]):i=l&&y(l)?l:{},s(f,{name:u,newValue:e(w,i,a)})):typeof a<"u"&&s(f,{name:u,newValue:a}));return f}},function(g,v,c){Object.defineProperty(v,"__esModule",{value:!0}),v.default=v.BlockEmbed=v.bubbleFormats=void 0;var O=function(){function d(_,N){for(var x=0;x<N.length;x++){var R=N[x];R.enumerable=R.enumerable||!1,R.configurable=!0,"value"in R&&(R.writable=!0),Object.defineProperty(_,R.key,R)}}return function(_,N,x){return N&&d(_.prototype,N),x&&d(_,x),_}}(),m=function d(_,N,x){_===null&&(_=Function.prototype);var R=Object.getOwnPropertyDescriptor(_,N);if(R===void 0){var F=Object.getPrototypeOf(_);return F===null?void 0:d(F,N,x)}else{if("value"in R)return R.value;var U=R.get;return U===void 0?void 0:U.call(x)}},b=c(3),p=f(b),y=c(2),s=f(y),o=c(0),e=f(o),t=c(16),u=f(t),l=c(6),a=f(l),r=c(7),i=f(r);function f(d){return d&&d.__esModule?d:{default:d}}function n(d,_){if(!(d instanceof _))throw new TypeError("Cannot call a class as a function")}function h(d,_){if(!d)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return _&&(typeof _=="object"||typeof _=="function")?_:d}function w(d,_){if(typeof _!="function"&&_!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof _);d.prototype=Object.create(_&&_.prototype,{constructor:{value:d,enumerable:!1,writable:!0,configurable:!0}}),_&&(Object.setPrototypeOf?Object.setPrototypeOf(d,_):d.__proto__=_)}var A=1,k=function(d){w(_,d);function _(){return n(this,_),h(this,(_.__proto__||Object.getPrototypeOf(_)).apply(this,arguments))}return O(_,[{key:"attach",value:function(){m(_.prototype.__proto__||Object.getPrototypeOf(_.prototype),"attach",this).call(this),this.attributes=new e.default.Attributor.Store(this.domNode)}},{key:"delta",value:function(){return new s.default().insert(this.value(),(0,p.default)(this.formats(),this.attributes.values()))}},{key:"format",value:function(x,R){var F=e.default.query(x,e.default.Scope.BLOCK_ATTRIBUTE);F!=null&&this.attributes.attribute(F,R)}},{key:"formatAt",value:function(x,R,F,U){this.format(F,U)}},{key:"insertAt",value:function(x,R,F){if(typeof R=="string"&&R.endsWith(`
`)){var U=e.default.create(S.blotName);this.parent.insertBefore(U,x===0?this:this.next),U.insertAt(0,R.slice(0,-1))}else m(_.prototype.__proto__||Object.getPrototypeOf(_.prototype),"insertAt",this).call(this,x,R,F)}}]),_}(e.default.Embed);k.scope=e.default.Scope.BLOCK_BLOT;var S=function(d){w(_,d);function _(N){n(this,_);var x=h(this,(_.__proto__||Object.getPrototypeOf(_)).call(this,N));return x.cache={},x}return O(_,[{key:"delta",value:function(){return this.cache.delta==null&&(this.cache.delta=this.descendants(e.default.Leaf).reduce(function(x,R){return R.length()===0?x:x.insert(R.value(),E(R))},new s.default).insert(`
`,E(this))),this.cache.delta}},{key:"deleteAt",value:function(x,R){m(_.prototype.__proto__||Object.getPrototypeOf(_.prototype),"deleteAt",this).call(this,x,R),this.cache={}}},{key:"formatAt",value:function(x,R,F,U){R<=0||(e.default.query(F,e.default.Scope.BLOCK)?x+R===this.length()&&this.format(F,U):m(_.prototype.__proto__||Object.getPrototypeOf(_.prototype),"formatAt",this).call(this,x,Math.min(R,this.length()-x-1),F,U),this.cache={})}},{key:"insertAt",value:function(x,R,F){if(F!=null)return m(_.prototype.__proto__||Object.getPrototypeOf(_.prototype),"insertAt",this).call(this,x,R,F);if(R.length!==0){var U=R.split(`
`),W=U.shift();W.length>0&&(x<this.length()-1||this.children.tail==null?m(_.prototype.__proto__||Object.getPrototypeOf(_.prototype),"insertAt",this).call(this,Math.min(x,this.length()-1),W):this.children.tail.insertAt(this.children.tail.length(),W),this.cache={});var D=this;U.reduce(function(M,T){return D=D.split(M,!0),D.insertAt(0,T),T.length},x+W.length)}}},{key:"insertBefore",value:function(x,R){var F=this.children.head;m(_.prototype.__proto__||Object.getPrototypeOf(_.prototype),"insertBefore",this).call(this,x,R),F instanceof u.default&&F.remove(),this.cache={}}},{key:"length",value:function(){return this.cache.length==null&&(this.cache.length=m(_.prototype.__proto__||Object.getPrototypeOf(_.prototype),"length",this).call(this)+A),this.cache.length}},{key:"moveChildren",value:function(x,R){m(_.prototype.__proto__||Object.getPrototypeOf(_.prototype),"moveChildren",this).call(this,x,R),this.cache={}}},{key:"optimize",value:function(x){m(_.prototype.__proto__||Object.getPrototypeOf(_.prototype),"optimize",this).call(this,x),this.cache={}}},{key:"path",value:function(x){return m(_.prototype.__proto__||Object.getPrototypeOf(_.prototype),"path",this).call(this,x,!0)}},{key:"removeChild",value:function(x){m(_.prototype.__proto__||Object.getPrototypeOf(_.prototype),"removeChild",this).call(this,x),this.cache={}}},{key:"split",value:function(x){var R=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1;if(R&&(x===0||x>=this.length()-A)){var F=this.clone();return x===0?(this.parent.insertBefore(F,this),this):(this.parent.insertBefore(F,this.next),F)}else{var U=m(_.prototype.__proto__||Object.getPrototypeOf(_.prototype),"split",this).call(this,x,R);return this.cache={},U}}}]),_}(e.default.Block);S.blotName="block",S.tagName="P",S.defaultChild="break",S.allowedChildren=[a.default,e.default.Embed,i.default];function E(d){var _=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};return d==null||(typeof d.formats=="function"&&(_=(0,p.default)(_,d.formats())),d.parent==null||d.parent.blotName=="scroll"||d.parent.statics.scope!==d.statics.scope)?_:E(d.parent,_)}v.bubbleFormats=E,v.BlockEmbed=k,v.default=S},function(g,v,c){Object.defineProperty(v,"__esModule",{value:!0}),v.default=v.overload=v.expandConfig=void 0;var O=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(D){return typeof D}:function(D){return D&&typeof Symbol=="function"&&D.constructor===Symbol&&D!==Symbol.prototype?"symbol":typeof D},m=function(){function D(M,T){var q=[],j=!0,H=!1,C=void 0;try{for(var P=M[Symbol.iterator](),I;!(j=(I=P.next()).done)&&(q.push(I.value),!(T&&q.length===T));j=!0);}catch(z){H=!0,C=z}finally{try{!j&&P.return&&P.return()}finally{if(H)throw C}}return q}return function(M,T){if(Array.isArray(M))return M;if(Symbol.iterator in Object(M))return D(M,T);throw new TypeError("Invalid attempt to destructure non-iterable instance")}}(),b=function(){function D(M,T){for(var q=0;q<T.length;q++){var j=T[q];j.enumerable=j.enumerable||!1,j.configurable=!0,"value"in j&&(j.writable=!0),Object.defineProperty(M,j.key,j)}}return function(M,T,q){return T&&D(M.prototype,T),q&&D(M,q),M}}();c(50);var p=c(2),y=E(p),s=c(14),o=E(s),e=c(8),t=E(e),u=c(9),l=E(u),a=c(0),r=E(a),i=c(15),f=E(i),n=c(3),h=E(n),w=c(10),A=E(w),k=c(34),S=E(k);function E(D){return D&&D.__esModule?D:{default:D}}function d(D,M,T){return M in D?Object.defineProperty(D,M,{value:T,enumerable:!0,configurable:!0,writable:!0}):D[M]=T,D}function _(D,M){if(!(D instanceof M))throw new TypeError("Cannot call a class as a function")}var N=(0,A.default)("quill"),x=function(){b(D,null,[{key:"debug",value:function(T){T===!0&&(T="log"),A.default.level(T)}},{key:"find",value:function(T){return T.__quill||r.default.find(T)}},{key:"import",value:function(T){return this.imports[T]==null&&N.error("Cannot import "+T+". Are you sure it was registered?"),this.imports[T]}},{key:"register",value:function(T,q){var j=this,H=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!1;if(typeof T!="string"){var C=T.attrName||T.blotName;typeof C=="string"?this.register("formats/"+C,T,q):Object.keys(T).forEach(function(P){j.register(P,T[P],q)})}else this.imports[T]!=null&&!H&&N.warn("Overwriting "+T+" with",q),this.imports[T]=q,(T.startsWith("blots/")||T.startsWith("formats/"))&&q.blotName!=="abstract"?r.default.register(q):T.startsWith("modules")&&typeof q.register=="function"&&q.register()}}]);function D(M){var T=this,q=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};if(_(this,D),this.options=R(M,q),this.container=this.options.container,this.container==null)return N.error("Invalid Quill container",M);this.options.debug&&D.debug(this.options.debug);var j=this.container.innerHTML.trim();this.container.classList.add("ql-container"),this.container.innerHTML="",this.container.__quill=this,this.root=this.addContainer("ql-editor"),this.root.classList.add("ql-blank"),this.root.setAttribute("data-gramm",!1),this.scrollingContainer=this.options.scrollingContainer||this.root,this.emitter=new t.default,this.scroll=r.default.create(this.root,{emitter:this.emitter,whitelist:this.options.formats}),this.editor=new o.default(this.scroll),this.selection=new f.default(this.scroll,this.emitter),this.theme=new this.options.theme(this,this.options),this.keyboard=this.theme.addModule("keyboard"),this.clipboard=this.theme.addModule("clipboard"),this.history=this.theme.addModule("history"),this.theme.init(),this.emitter.on(t.default.events.EDITOR_CHANGE,function(C){C===t.default.events.TEXT_CHANGE&&T.root.classList.toggle("ql-blank",T.editor.isBlank())}),this.emitter.on(t.default.events.SCROLL_UPDATE,function(C,P){var I=T.selection.lastRange,z=I&&I.length===0?I.index:void 0;F.call(T,function(){return T.editor.update(null,P,z)},C)});var H=this.clipboard.convert(`<div class='ql-editor' style="white-space: normal;">`+j+"<p><br></p></div>");this.setContents(H),this.history.clear(),this.options.placeholder&&this.root.setAttribute("data-placeholder",this.options.placeholder),this.options.readOnly&&this.disable()}return b(D,[{key:"addContainer",value:function(T){var q=arguments.length>1&&arguments[1]!==void 0?arguments[1]:null;if(typeof T=="string"){var j=T;T=document.createElement("div"),T.classList.add(j)}return this.container.insertBefore(T,q),T}},{key:"blur",value:function(){this.selection.setRange(null)}},{key:"deleteText",value:function(T,q,j){var H=this,C=U(T,q,j),P=m(C,4);return T=P[0],q=P[1],j=P[3],F.call(this,function(){return H.editor.deleteText(T,q)},j,T,-1*q)}},{key:"disable",value:function(){this.enable(!1)}},{key:"enable",value:function(){var T=arguments.length>0&&arguments[0]!==void 0?arguments[0]:!0;this.scroll.enable(T),this.container.classList.toggle("ql-disabled",!T)}},{key:"focus",value:function(){var T=this.scrollingContainer.scrollTop;this.selection.focus(),this.scrollingContainer.scrollTop=T,this.scrollIntoView()}},{key:"format",value:function(T,q){var j=this,H=arguments.length>2&&arguments[2]!==void 0?arguments[2]:t.default.sources.API;return F.call(this,function(){var C=j.getSelection(!0),P=new y.default;if(C==null)return P;if(r.default.query(T,r.default.Scope.BLOCK))P=j.editor.formatLine(C.index,C.length,d({},T,q));else{if(C.length===0)return j.selection.format(T,q),P;P=j.editor.formatText(C.index,C.length,d({},T,q))}return j.setSelection(C,t.default.sources.SILENT),P},H)}},{key:"formatLine",value:function(T,q,j,H,C){var P=this,I=void 0,z=U(T,q,j,H,C),K=m(z,4);return T=K[0],q=K[1],I=K[2],C=K[3],F.call(this,function(){return P.editor.formatLine(T,q,I)},C,T,0)}},{key:"formatText",value:function(T,q,j,H,C){var P=this,I=void 0,z=U(T,q,j,H,C),K=m(z,4);return T=K[0],q=K[1],I=K[2],C=K[3],F.call(this,function(){return P.editor.formatText(T,q,I)},C,T,0)}},{key:"getBounds",value:function(T){var q=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0,j=void 0;typeof T=="number"?j=this.selection.getBounds(T,q):j=this.selection.getBounds(T.index,T.length);var H=this.container.getBoundingClientRect();return{bottom:j.bottom-H.top,height:j.height,left:j.left-H.left,right:j.right-H.left,top:j.top-H.top,width:j.width}}},{key:"getContents",value:function(){var T=arguments.length>0&&arguments[0]!==void 0?arguments[0]:0,q=arguments.length>1&&arguments[1]!==void 0?arguments[1]:this.getLength()-T,j=U(T,q),H=m(j,2);return T=H[0],q=H[1],this.editor.getContents(T,q)}},{key:"getFormat",value:function(){var T=arguments.length>0&&arguments[0]!==void 0?arguments[0]:this.getSelection(!0),q=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0;return typeof T=="number"?this.editor.getFormat(T,q):this.editor.getFormat(T.index,T.length)}},{key:"getIndex",value:function(T){return T.offset(this.scroll)}},{key:"getLength",value:function(){return this.scroll.length()}},{key:"getLeaf",value:function(T){return this.scroll.leaf(T)}},{key:"getLine",value:function(T){return this.scroll.line(T)}},{key:"getLines",value:function(){var T=arguments.length>0&&arguments[0]!==void 0?arguments[0]:0,q=arguments.length>1&&arguments[1]!==void 0?arguments[1]:Number.MAX_VALUE;return typeof T!="number"?this.scroll.lines(T.index,T.length):this.scroll.lines(T,q)}},{key:"getModule",value:function(T){return this.theme.modules[T]}},{key:"getSelection",value:function(){var T=arguments.length>0&&arguments[0]!==void 0?arguments[0]:!1;return T&&this.focus(),this.update(),this.selection.getRange()[0]}},{key:"getText",value:function(){var T=arguments.length>0&&arguments[0]!==void 0?arguments[0]:0,q=arguments.length>1&&arguments[1]!==void 0?arguments[1]:this.getLength()-T,j=U(T,q),H=m(j,2);return T=H[0],q=H[1],this.editor.getText(T,q)}},{key:"hasFocus",value:function(){return this.selection.hasFocus()}},{key:"insertEmbed",value:function(T,q,j){var H=this,C=arguments.length>3&&arguments[3]!==void 0?arguments[3]:D.sources.API;return F.call(this,function(){return H.editor.insertEmbed(T,q,j)},C,T)}},{key:"insertText",value:function(T,q,j,H,C){var P=this,I=void 0,z=U(T,0,j,H,C),K=m(z,4);return T=K[0],I=K[2],C=K[3],F.call(this,function(){return P.editor.insertText(T,q,I)},C,T,q.length)}},{key:"isEnabled",value:function(){return!this.container.classList.contains("ql-disabled")}},{key:"off",value:function(){return this.emitter.off.apply(this.emitter,arguments)}},{key:"on",value:function(){return this.emitter.on.apply(this.emitter,arguments)}},{key:"once",value:function(){return this.emitter.once.apply(this.emitter,arguments)}},{key:"pasteHTML",value:function(T,q,j){this.clipboard.dangerouslyPasteHTML(T,q,j)}},{key:"removeFormat",value:function(T,q,j){var H=this,C=U(T,q,j),P=m(C,4);return T=P[0],q=P[1],j=P[3],F.call(this,function(){return H.editor.removeFormat(T,q)},j,T)}},{key:"scrollIntoView",value:function(){this.selection.scrollIntoView(this.scrollingContainer)}},{key:"setContents",value:function(T){var q=this,j=arguments.length>1&&arguments[1]!==void 0?arguments[1]:t.default.sources.API;return F.call(this,function(){T=new y.default(T);var H=q.getLength(),C=q.editor.deleteText(0,H),P=q.editor.applyDelta(T),I=P.ops[P.ops.length-1];I!=null&&typeof I.insert=="string"&&I.insert[I.insert.length-1]===`
`&&(q.editor.deleteText(q.getLength()-1,1),P.delete(1));var z=C.compose(P);return z},j)}},{key:"setSelection",value:function(T,q,j){if(T==null)this.selection.setRange(null,q||D.sources.API);else{var H=U(T,q,j),C=m(H,4);T=C[0],q=C[1],j=C[3],this.selection.setRange(new i.Range(T,q),j),j!==t.default.sources.SILENT&&this.selection.scrollIntoView(this.scrollingContainer)}}},{key:"setText",value:function(T){var q=arguments.length>1&&arguments[1]!==void 0?arguments[1]:t.default.sources.API,j=new y.default().insert(T);return this.setContents(j,q)}},{key:"update",value:function(){var T=arguments.length>0&&arguments[0]!==void 0?arguments[0]:t.default.sources.USER,q=this.scroll.update(T);return this.selection.update(T),q}},{key:"updateContents",value:function(T){var q=this,j=arguments.length>1&&arguments[1]!==void 0?arguments[1]:t.default.sources.API;return F.call(this,function(){return T=new y.default(T),q.editor.applyDelta(T,j)},j,!0)}}]),D}();x.DEFAULTS={bounds:null,formats:null,modules:{},placeholder:"",readOnly:!1,scrollingContainer:null,strict:!0,theme:"default"},x.events=t.default.events,x.sources=t.default.sources,x.version="1.3.7",x.imports={delta:y.default,parchment:r.default,"core/module":l.default,"core/theme":S.default};function R(D,M){if(M=(0,h.default)(!0,{container:D,modules:{clipboard:!0,keyboard:!0,history:!0}},M),!M.theme||M.theme===x.DEFAULTS.theme)M.theme=S.default;else if(M.theme=x.import("themes/"+M.theme),M.theme==null)throw new Error("Invalid theme "+M.theme+". Did you register it?");var T=(0,h.default)(!0,{},M.theme.DEFAULTS);[T,M].forEach(function(H){H.modules=H.modules||{},Object.keys(H.modules).forEach(function(C){H.modules[C]===!0&&(H.modules[C]={})})});var q=Object.keys(T.modules).concat(Object.keys(M.modules)),j=q.reduce(function(H,C){var P=x.import("modules/"+C);return P==null?N.error("Cannot load "+C+" module. Are you sure you registered it?"):H[C]=P.DEFAULTS||{},H},{});return M.modules!=null&&M.modules.toolbar&&M.modules.toolbar.constructor!==Object&&(M.modules.toolbar={container:M.modules.toolbar}),M=(0,h.default)(!0,{},x.DEFAULTS,{modules:j},T,M),["bounds","container","scrollingContainer"].forEach(function(H){typeof M[H]=="string"&&(M[H]=document.querySelector(M[H]))}),M.modules=Object.keys(M.modules).reduce(function(H,C){return M.modules[C]&&(H[C]=M.modules[C]),H},{}),M}function F(D,M,T,q){if(this.options.strict&&!this.isEnabled()&&M===t.default.sources.USER)return new y.default;var j=T==null?null:this.getSelection(),H=this.editor.delta,C=D();if(j!=null&&(T===!0&&(T=j.index),q==null?j=W(j,C,M):q!==0&&(j=W(j,T,q,M)),this.setSelection(j,t.default.sources.SILENT)),C.length()>0){var P,I=[t.default.events.TEXT_CHANGE,C,H,M];if((P=this.emitter).emit.apply(P,[t.default.events.EDITOR_CHANGE].concat(I)),M!==t.default.sources.SILENT){var z;(z=this.emitter).emit.apply(z,I)}}return C}function U(D,M,T,q,j){var H={};return typeof D.index=="number"&&typeof D.length=="number"?typeof M!="number"?(j=q,q=T,T=M,M=D.length,D=D.index):(M=D.length,D=D.index):typeof M!="number"&&(j=q,q=T,T=M,M=0),(typeof T>"u"?"undefined":O(T))==="object"?(H=T,j=q):typeof T=="string"&&(q!=null?H[T]=q:j=T),j=j||t.default.sources.API,[D,M,H,j]}function W(D,M,T,q){if(D==null)return null;var j=void 0,H=void 0;if(M instanceof y.default){var C=[D.index,D.index+D.length].map(function(K){return M.transformPosition(K,q!==t.default.sources.USER)}),P=m(C,2);j=P[0],H=P[1]}else{var I=[D.index,D.index+D.length].map(function(K){return K<M||K===M&&q===t.default.sources.USER?K:T>=0?K+T:Math.max(M,K+T)}),z=m(I,2);j=z[0],H=z[1]}return new i.Range(j,H-j)}v.expandConfig=R,v.overload=U,v.default=x},function(g,v,c){Object.defineProperty(v,"__esModule",{value:!0});var O=function(){function a(r,i){for(var f=0;f<i.length;f++){var n=i[f];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(r,n.key,n)}}return function(r,i,f){return i&&a(r.prototype,i),f&&a(r,f),r}}(),m=function a(r,i,f){r===null&&(r=Function.prototype);var n=Object.getOwnPropertyDescriptor(r,i);if(n===void 0){var h=Object.getPrototypeOf(r);return h===null?void 0:a(h,i,f)}else{if("value"in n)return n.value;var w=n.get;return w===void 0?void 0:w.call(f)}},b=c(7),p=o(b),y=c(0),s=o(y);function o(a){return a&&a.__esModule?a:{default:a}}function e(a,r){if(!(a instanceof r))throw new TypeError("Cannot call a class as a function")}function t(a,r){if(!a)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return r&&(typeof r=="object"||typeof r=="function")?r:a}function u(a,r){if(typeof r!="function"&&r!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof r);a.prototype=Object.create(r&&r.prototype,{constructor:{value:a,enumerable:!1,writable:!0,configurable:!0}}),r&&(Object.setPrototypeOf?Object.setPrototypeOf(a,r):a.__proto__=r)}var l=function(a){u(r,a);function r(){return e(this,r),t(this,(r.__proto__||Object.getPrototypeOf(r)).apply(this,arguments))}return O(r,[{key:"formatAt",value:function(f,n,h,w){if(r.compare(this.statics.blotName,h)<0&&s.default.query(h,s.default.Scope.BLOT)){var A=this.isolate(f,n);w&&A.wrap(h,w)}else m(r.prototype.__proto__||Object.getPrototypeOf(r.prototype),"formatAt",this).call(this,f,n,h,w)}},{key:"optimize",value:function(f){if(m(r.prototype.__proto__||Object.getPrototypeOf(r.prototype),"optimize",this).call(this,f),this.parent instanceof r&&r.compare(this.statics.blotName,this.parent.statics.blotName)>0){var n=this.parent.isolate(this.offset(),this.length());this.moveChildren(n),n.wrap(this)}}}],[{key:"compare",value:function(f,n){var h=r.order.indexOf(f),w=r.order.indexOf(n);return h>=0||w>=0?h-w:f===n?0:f<n?-1:1}}]),r}(s.default.Inline);l.allowedChildren=[l,s.default.Embed,p.default],l.order=["cursor","inline","underline","strike","italic","bold","script","link","code"],v.default=l},function(g,v,c){Object.defineProperty(v,"__esModule",{value:!0});var O=c(0),m=b(O);function b(e){return e&&e.__esModule?e:{default:e}}function p(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function y(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t&&(typeof t=="object"||typeof t=="function")?t:e}function s(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}var o=function(e){s(t,e);function t(){return p(this,t),y(this,(t.__proto__||Object.getPrototypeOf(t)).apply(this,arguments))}return t}(m.default.Text);v.default=o},function(g,v,c){Object.defineProperty(v,"__esModule",{value:!0});var O=function(){function i(f,n){for(var h=0;h<n.length;h++){var w=n[h];w.enumerable=w.enumerable||!1,w.configurable=!0,"value"in w&&(w.writable=!0),Object.defineProperty(f,w.key,w)}}return function(f,n,h){return n&&i(f.prototype,n),h&&i(f,h),f}}(),m=function i(f,n,h){f===null&&(f=Function.prototype);var w=Object.getOwnPropertyDescriptor(f,n);if(w===void 0){var A=Object.getPrototypeOf(f);return A===null?void 0:i(A,n,h)}else{if("value"in w)return w.value;var k=w.get;return k===void 0?void 0:k.call(h)}},b=c(54),p=o(b),y=c(10),s=o(y);function o(i){return i&&i.__esModule?i:{default:i}}function e(i,f){if(!(i instanceof f))throw new TypeError("Cannot call a class as a function")}function t(i,f){if(!i)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return f&&(typeof f=="object"||typeof f=="function")?f:i}function u(i,f){if(typeof f!="function"&&f!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof f);i.prototype=Object.create(f&&f.prototype,{constructor:{value:i,enumerable:!1,writable:!0,configurable:!0}}),f&&(Object.setPrototypeOf?Object.setPrototypeOf(i,f):i.__proto__=f)}var l=(0,s.default)("quill:events"),a=["selectionchange","mousedown","mouseup","click"];a.forEach(function(i){document.addEventListener(i,function(){for(var f=arguments.length,n=Array(f),h=0;h<f;h++)n[h]=arguments[h];[].slice.call(document.querySelectorAll(".ql-container")).forEach(function(w){if(w.__quill&&w.__quill.emitter){var A;(A=w.__quill.emitter).handleDOM.apply(A,n)}})})});var r=function(i){u(f,i);function f(){e(this,f);var n=t(this,(f.__proto__||Object.getPrototypeOf(f)).call(this));return n.listeners={},n.on("error",l.error),n}return O(f,[{key:"emit",value:function(){l.log.apply(l,arguments),m(f.prototype.__proto__||Object.getPrototypeOf(f.prototype),"emit",this).apply(this,arguments)}},{key:"handleDOM",value:function(h){for(var w=arguments.length,A=Array(w>1?w-1:0),k=1;k<w;k++)A[k-1]=arguments[k];(this.listeners[h.type]||[]).forEach(function(S){var E=S.node,d=S.handler;(h.target===E||E.contains(h.target))&&d.apply(void 0,[h].concat(A))})}},{key:"listenDOM",value:function(h,w,A){this.listeners[h]||(this.listeners[h]=[]),this.listeners[h].push({node:w,handler:A})}}]),f}(p.default);r.events={EDITOR_CHANGE:"editor-change",SCROLL_BEFORE_UPDATE:"scroll-before-update",SCROLL_OPTIMIZE:"scroll-optimize",SCROLL_UPDATE:"scroll-update",SELECTION_CHANGE:"selection-change",TEXT_CHANGE:"text-change"},r.sources={API:"api",SILENT:"silent",USER:"user"},v.default=r},function(g,v,c){Object.defineProperty(v,"__esModule",{value:!0});function O(b,p){if(!(b instanceof p))throw new TypeError("Cannot call a class as a function")}var m=function b(p){var y=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};O(this,b),this.quill=p,this.options=y};m.DEFAULTS={},v.default=m},function(g,v,c){Object.defineProperty(v,"__esModule",{value:!0});var O=["error","warn","log","info"],m="warn";function b(y){if(O.indexOf(y)<=O.indexOf(m)){for(var s,o=arguments.length,e=Array(o>1?o-1:0),t=1;t<o;t++)e[t-1]=arguments[t];(s=console)[y].apply(s,e)}}function p(y){return O.reduce(function(s,o){return s[o]=b.bind(console,o,y),s},{})}b.level=p.level=function(y){m=y},v.default=p},function(g,v,c){var O=Array.prototype.slice,m=c(52),b=c(53),p=g.exports=function(e,t,u){return u||(u={}),e===t?!0:e instanceof Date&&t instanceof Date?e.getTime()===t.getTime():!e||!t||typeof e!="object"&&typeof t!="object"?u.strict?e===t:e==t:o(e,t,u)};function y(e){return e==null}function s(e){return!(!e||typeof e!="object"||typeof e.length!="number"||typeof e.copy!="function"||typeof e.slice!="function"||e.length>0&&typeof e[0]!="number")}function o(e,t,u){var l,a;if(y(e)||y(t)||e.prototype!==t.prototype)return!1;if(b(e))return b(t)?(e=O.call(e),t=O.call(t),p(e,t,u)):!1;if(s(e)){if(!s(t)||e.length!==t.length)return!1;for(l=0;l<e.length;l++)if(e[l]!==t[l])return!1;return!0}try{var r=m(e),i=m(t)}catch{return!1}if(r.length!=i.length)return!1;for(r.sort(),i.sort(),l=r.length-1;l>=0;l--)if(r[l]!=i[l])return!1;for(l=r.length-1;l>=0;l--)if(a=r[l],!p(e[a],t[a],u))return!1;return typeof e==typeof t}},function(g,v,c){Object.defineProperty(v,"__esModule",{value:!0});var O=c(1),m=function(){function b(p,y,s){s===void 0&&(s={}),this.attrName=p,this.keyName=y;var o=O.Scope.TYPE&O.Scope.ATTRIBUTE;s.scope!=null?this.scope=s.scope&O.Scope.LEVEL|o:this.scope=O.Scope.ATTRIBUTE,s.whitelist!=null&&(this.whitelist=s.whitelist)}return b.keys=function(p){return[].map.call(p.attributes,function(y){return y.name})},b.prototype.add=function(p,y){return this.canAdd(p,y)?(p.setAttribute(this.keyName,y),!0):!1},b.prototype.canAdd=function(p,y){var s=O.query(p,O.Scope.BLOT&(this.scope|O.Scope.TYPE));return s==null?!1:this.whitelist==null?!0:typeof y=="string"?this.whitelist.indexOf(y.replace(/["']/g,""))>-1:this.whitelist.indexOf(y)>-1},b.prototype.remove=function(p){p.removeAttribute(this.keyName)},b.prototype.value=function(p){var y=p.getAttribute(this.keyName);return this.canAdd(p,y)&&y?y:""},b}();v.default=m},function(g,v,c){Object.defineProperty(v,"__esModule",{value:!0}),v.default=v.Code=void 0;var O=function(){function k(S,E){var d=[],_=!0,N=!1,x=void 0;try{for(var R=S[Symbol.iterator](),F;!(_=(F=R.next()).done)&&(d.push(F.value),!(E&&d.length===E));_=!0);}catch(U){N=!0,x=U}finally{try{!_&&R.return&&R.return()}finally{if(N)throw x}}return d}return function(S,E){if(Array.isArray(S))return S;if(Symbol.iterator in Object(S))return k(S,E);throw new TypeError("Invalid attempt to destructure non-iterable instance")}}(),m=function(){function k(S,E){for(var d=0;d<E.length;d++){var _=E[d];_.enumerable=_.enumerable||!1,_.configurable=!0,"value"in _&&(_.writable=!0),Object.defineProperty(S,_.key,_)}}return function(S,E,d){return E&&k(S.prototype,E),d&&k(S,d),S}}(),b=function k(S,E,d){S===null&&(S=Function.prototype);var _=Object.getOwnPropertyDescriptor(S,E);if(_===void 0){var N=Object.getPrototypeOf(S);return N===null?void 0:k(N,E,d)}else{if("value"in _)return _.value;var x=_.get;return x===void 0?void 0:x.call(d)}},p=c(2),y=i(p),s=c(0),o=i(s),e=c(4),t=i(e),u=c(6),l=i(u),a=c(7),r=i(a);function i(k){return k&&k.__esModule?k:{default:k}}function f(k,S){if(!(k instanceof S))throw new TypeError("Cannot call a class as a function")}function n(k,S){if(!k)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return S&&(typeof S=="object"||typeof S=="function")?S:k}function h(k,S){if(typeof S!="function"&&S!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof S);k.prototype=Object.create(S&&S.prototype,{constructor:{value:k,enumerable:!1,writable:!0,configurable:!0}}),S&&(Object.setPrototypeOf?Object.setPrototypeOf(k,S):k.__proto__=S)}var w=function(k){h(S,k);function S(){return f(this,S),n(this,(S.__proto__||Object.getPrototypeOf(S)).apply(this,arguments))}return S}(l.default);w.blotName="code",w.tagName="CODE";var A=function(k){h(S,k);function S(){return f(this,S),n(this,(S.__proto__||Object.getPrototypeOf(S)).apply(this,arguments))}return m(S,[{key:"delta",value:function(){var d=this,_=this.domNode.textContent;return _.endsWith(`
`)&&(_=_.slice(0,-1)),_.split(`
`).reduce(function(N,x){return N.insert(x).insert(`
`,d.formats())},new y.default)}},{key:"format",value:function(d,_){if(!(d===this.statics.blotName&&_)){var N=this.descendant(r.default,this.length()-1),x=O(N,1),R=x[0];R!=null&&R.deleteAt(R.length()-1,1),b(S.prototype.__proto__||Object.getPrototypeOf(S.prototype),"format",this).call(this,d,_)}}},{key:"formatAt",value:function(d,_,N,x){if(_!==0&&!(o.default.query(N,o.default.Scope.BLOCK)==null||N===this.statics.blotName&&x===this.statics.formats(this.domNode))){var R=this.newlineIndex(d);if(!(R<0||R>=d+_)){var F=this.newlineIndex(d,!0)+1,U=R-F+1,W=this.isolate(F,U),D=W.next;W.format(N,x),D instanceof S&&D.formatAt(0,d-F+_-U,N,x)}}}},{key:"insertAt",value:function(d,_,N){if(N==null){var x=this.descendant(r.default,d),R=O(x,2),F=R[0],U=R[1];F.insertAt(U,_)}}},{key:"length",value:function(){var d=this.domNode.textContent.length;return this.domNode.textContent.endsWith(`
`)?d:d+1}},{key:"newlineIndex",value:function(d){var _=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1;if(_)return this.domNode.textContent.slice(0,d).lastIndexOf(`
`);var N=this.domNode.textContent.slice(d).indexOf(`
`);return N>-1?d+N:-1}},{key:"optimize",value:function(d){this.domNode.textContent.endsWith(`
`)||this.appendChild(o.default.create("text",`
`)),b(S.prototype.__proto__||Object.getPrototypeOf(S.prototype),"optimize",this).call(this,d);var _=this.next;_!=null&&_.prev===this&&_.statics.blotName===this.statics.blotName&&this.statics.formats(this.domNode)===_.statics.formats(_.domNode)&&(_.optimize(d),_.moveChildren(this),_.remove())}},{key:"replace",value:function(d){b(S.prototype.__proto__||Object.getPrototypeOf(S.prototype),"replace",this).call(this,d),[].slice.call(this.domNode.querySelectorAll("*")).forEach(function(_){var N=o.default.find(_);N==null?_.parentNode.removeChild(_):N instanceof o.default.Embed?N.remove():N.unwrap()})}}],[{key:"create",value:function(d){var _=b(S.__proto__||Object.getPrototypeOf(S),"create",this).call(this,d);return _.setAttribute("spellcheck",!1),_}},{key:"formats",value:function(){return!0}}]),S}(t.default);A.blotName="code-block",A.tagName="PRE",A.TAB="  ",v.Code=w,v.default=A},function(g,v,c){Object.defineProperty(v,"__esModule",{value:!0});var O=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(D){return typeof D}:function(D){return D&&typeof Symbol=="function"&&D.constructor===Symbol&&D!==Symbol.prototype?"symbol":typeof D},m=function(){function D(M,T){var q=[],j=!0,H=!1,C=void 0;try{for(var P=M[Symbol.iterator](),I;!(j=(I=P.next()).done)&&(q.push(I.value),!(T&&q.length===T));j=!0);}catch(z){H=!0,C=z}finally{try{!j&&P.return&&P.return()}finally{if(H)throw C}}return q}return function(M,T){if(Array.isArray(M))return M;if(Symbol.iterator in Object(M))return D(M,T);throw new TypeError("Invalid attempt to destructure non-iterable instance")}}(),b=function(){function D(M,T){for(var q=0;q<T.length;q++){var j=T[q];j.enumerable=j.enumerable||!1,j.configurable=!0,"value"in j&&(j.writable=!0),Object.defineProperty(M,j.key,j)}}return function(M,T,q){return T&&D(M.prototype,T),q&&D(M,q),M}}(),p=c(2),y=_(p),s=c(20),o=_(s),e=c(0),t=_(e),u=c(13),l=_(u),a=c(24),r=_(a),i=c(4),f=_(i),n=c(16),h=_(n),w=c(21),A=_(w),k=c(11),S=_(k),E=c(3),d=_(E);function _(D){return D&&D.__esModule?D:{default:D}}function N(D,M,T){return M in D?Object.defineProperty(D,M,{value:T,enumerable:!0,configurable:!0,writable:!0}):D[M]=T,D}function x(D,M){if(!(D instanceof M))throw new TypeError("Cannot call a class as a function")}var R=/^[ -~]*$/,F=function(){function D(M){x(this,D),this.scroll=M,this.delta=this.getDelta()}return b(D,[{key:"applyDelta",value:function(T){var q=this,j=!1;this.scroll.update();var H=this.scroll.length();return this.scroll.batchStart(),T=W(T),T.reduce(function(C,P){var I=P.retain||P.delete||P.insert.length||1,z=P.attributes||{};if(P.insert!=null){if(typeof P.insert=="string"){var K=P.insert;K.endsWith(`
`)&&j&&(j=!1,K=K.slice(0,-1)),C>=H&&!K.endsWith(`
`)&&(j=!0),q.scroll.insertAt(C,K);var Z=q.scroll.line(C),X=m(Z,2),J=X[0],ee=X[1],ie=(0,d.default)({},(0,i.bubbleFormats)(J));if(J instanceof f.default){var oe=J.descendant(t.default.Leaf,ee),se=m(oe,1),ue=se[0];ie=(0,d.default)(ie,(0,i.bubbleFormats)(ue))}z=o.default.attributes.diff(ie,z)||{}}else if(O(P.insert)==="object"){var V=Object.keys(P.insert)[0];if(V==null)return C;q.scroll.insertAt(C,V,P.insert[V])}H+=I}return Object.keys(z).forEach(function($){q.scroll.formatAt(C,I,$,z[$])}),C+I},0),T.reduce(function(C,P){return typeof P.delete=="number"?(q.scroll.deleteAt(C,P.delete),C):C+(P.retain||P.insert.length||1)},0),this.scroll.batchEnd(),this.update(T)}},{key:"deleteText",value:function(T,q){return this.scroll.deleteAt(T,q),this.update(new y.default().retain(T).delete(q))}},{key:"formatLine",value:function(T,q){var j=this,H=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{};return this.scroll.update(),Object.keys(H).forEach(function(C){if(!(j.scroll.whitelist!=null&&!j.scroll.whitelist[C])){var P=j.scroll.lines(T,Math.max(q,1)),I=q;P.forEach(function(z){var K=z.length();if(!(z instanceof l.default))z.format(C,H[C]);else{var Z=T-z.offset(j.scroll),X=z.newlineIndex(Z+I)-Z+1;z.formatAt(Z,X,C,H[C])}I-=K})}}),this.scroll.optimize(),this.update(new y.default().retain(T).retain(q,(0,A.default)(H)))}},{key:"formatText",value:function(T,q){var j=this,H=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{};return Object.keys(H).forEach(function(C){j.scroll.formatAt(T,q,C,H[C])}),this.update(new y.default().retain(T).retain(q,(0,A.default)(H)))}},{key:"getContents",value:function(T,q){return this.delta.slice(T,T+q)}},{key:"getDelta",value:function(){return this.scroll.lines().reduce(function(T,q){return T.concat(q.delta())},new y.default)}},{key:"getFormat",value:function(T){var q=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0,j=[],H=[];q===0?this.scroll.path(T).forEach(function(P){var I=m(P,1),z=I[0];z instanceof f.default?j.push(z):z instanceof t.default.Leaf&&H.push(z)}):(j=this.scroll.lines(T,q),H=this.scroll.descendants(t.default.Leaf,T,q));var C=[j,H].map(function(P){if(P.length===0)return{};for(var I=(0,i.bubbleFormats)(P.shift());Object.keys(I).length>0;){var z=P.shift();if(z==null)return I;I=U((0,i.bubbleFormats)(z),I)}return I});return d.default.apply(d.default,C)}},{key:"getText",value:function(T,q){return this.getContents(T,q).filter(function(j){return typeof j.insert=="string"}).map(function(j){return j.insert}).join("")}},{key:"insertEmbed",value:function(T,q,j){return this.scroll.insertAt(T,q,j),this.update(new y.default().retain(T).insert(N({},q,j)))}},{key:"insertText",value:function(T,q){var j=this,H=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{};return q=q.replace(/\r\n/g,`
`).replace(/\r/g,`
`),this.scroll.insertAt(T,q),Object.keys(H).forEach(function(C){j.scroll.formatAt(T,q.length,C,H[C])}),this.update(new y.default().retain(T).insert(q,(0,A.default)(H)))}},{key:"isBlank",value:function(){if(this.scroll.children.length==0)return!0;if(this.scroll.children.length>1)return!1;var T=this.scroll.children.head;return T.statics.blotName!==f.default.blotName||T.children.length>1?!1:T.children.head instanceof h.default}},{key:"removeFormat",value:function(T,q){var j=this.getText(T,q),H=this.scroll.line(T+q),C=m(H,2),P=C[0],I=C[1],z=0,K=new y.default;P!=null&&(P instanceof l.default?z=P.newlineIndex(I)-I+1:z=P.length()-I,K=P.delta().slice(I,I+z-1).insert(`
`));var Z=this.getContents(T,q+z),X=Z.diff(new y.default().insert(j).concat(K)),J=new y.default().retain(T).concat(X);return this.applyDelta(J)}},{key:"update",value:function(T){var q=arguments.length>1&&arguments[1]!==void 0?arguments[1]:[],j=arguments.length>2&&arguments[2]!==void 0?arguments[2]:void 0,H=this.delta;if(q.length===1&&q[0].type==="characterData"&&q[0].target.data.match(R)&&t.default.find(q[0].target)){var C=t.default.find(q[0].target),P=(0,i.bubbleFormats)(C),I=C.offset(this.scroll),z=q[0].oldValue.replace(r.default.CONTENTS,""),K=new y.default().insert(z),Z=new y.default().insert(C.value()),X=new y.default().retain(I).concat(K.diff(Z,j));T=X.reduce(function(J,ee){return ee.insert?J.insert(ee.insert,P):J.push(ee)},new y.default),this.delta=H.compose(T)}else this.delta=this.getDelta(),(!T||!(0,S.default)(H.compose(T),this.delta))&&(T=H.diff(this.delta,j));return T}}]),D}();function U(D,M){return Object.keys(M).reduce(function(T,q){return D[q]==null||(M[q]===D[q]?T[q]=M[q]:Array.isArray(M[q])?M[q].indexOf(D[q])<0&&(T[q]=M[q].concat([D[q]])):T[q]=[M[q],D[q]]),T},{})}function W(D){return D.reduce(function(M,T){if(T.insert===1){var q=(0,A.default)(T.attributes);return delete q.image,M.insert({image:T.attributes.image},q)}if(T.attributes!=null&&(T.attributes.list===!0||T.attributes.bullet===!0)&&(T=(0,A.default)(T),T.attributes.list?T.attributes.list="ordered":(T.attributes.list="bullet",delete T.attributes.bullet)),typeof T.insert=="string"){var j=T.insert.replace(/\r\n/g,`
`).replace(/\r/g,`
`);return M.insert(j,T.attributes)}return M.push(T)},new y.default)}v.default=F},function(g,v,c){Object.defineProperty(v,"__esModule",{value:!0}),v.default=v.Range=void 0;var O=function(){function k(S,E){var d=[],_=!0,N=!1,x=void 0;try{for(var R=S[Symbol.iterator](),F;!(_=(F=R.next()).done)&&(d.push(F.value),!(E&&d.length===E));_=!0);}catch(U){N=!0,x=U}finally{try{!_&&R.return&&R.return()}finally{if(N)throw x}}return d}return function(S,E){if(Array.isArray(S))return S;if(Symbol.iterator in Object(S))return k(S,E);throw new TypeError("Invalid attempt to destructure non-iterable instance")}}(),m=function(){function k(S,E){for(var d=0;d<E.length;d++){var _=E[d];_.enumerable=_.enumerable||!1,_.configurable=!0,"value"in _&&(_.writable=!0),Object.defineProperty(S,_.key,_)}}return function(S,E,d){return E&&k(S.prototype,E),d&&k(S,d),S}}(),b=c(0),p=r(b),y=c(21),s=r(y),o=c(11),e=r(o),t=c(8),u=r(t),l=c(10),a=r(l);function r(k){return k&&k.__esModule?k:{default:k}}function i(k){if(Array.isArray(k)){for(var S=0,E=Array(k.length);S<k.length;S++)E[S]=k[S];return E}else return Array.from(k)}function f(k,S){if(!(k instanceof S))throw new TypeError("Cannot call a class as a function")}var n=(0,a.default)("quill:selection"),h=function k(S){var E=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0;f(this,k),this.index=S,this.length=E},w=function(){function k(S,E){var d=this;f(this,k),this.emitter=E,this.scroll=S,this.composing=!1,this.mouseDown=!1,this.root=this.scroll.domNode,this.cursor=p.default.create("cursor",this),this.lastRange=this.savedRange=new h(0,0),this.handleComposition(),this.handleDragging(),this.emitter.listenDOM("selectionchange",document,function(){d.mouseDown||setTimeout(d.update.bind(d,u.default.sources.USER),1)}),this.emitter.on(u.default.events.EDITOR_CHANGE,function(_,N){_===u.default.events.TEXT_CHANGE&&N.length()>0&&d.update(u.default.sources.SILENT)}),this.emitter.on(u.default.events.SCROLL_BEFORE_UPDATE,function(){if(d.hasFocus()){var _=d.getNativeRange();_!=null&&_.start.node!==d.cursor.textNode&&d.emitter.once(u.default.events.SCROLL_UPDATE,function(){try{d.setNativeRange(_.start.node,_.start.offset,_.end.node,_.end.offset)}catch{}})}}),this.emitter.on(u.default.events.SCROLL_OPTIMIZE,function(_,N){if(N.range){var x=N.range,R=x.startNode,F=x.startOffset,U=x.endNode,W=x.endOffset;d.setNativeRange(R,F,U,W)}}),this.update(u.default.sources.SILENT)}return m(k,[{key:"handleComposition",value:function(){var E=this;this.root.addEventListener("compositionstart",function(){E.composing=!0}),this.root.addEventListener("compositionend",function(){if(E.composing=!1,E.cursor.parent){var d=E.cursor.restore();if(!d)return;setTimeout(function(){E.setNativeRange(d.startNode,d.startOffset,d.endNode,d.endOffset)},1)}})}},{key:"handleDragging",value:function(){var E=this;this.emitter.listenDOM("mousedown",document.body,function(){E.mouseDown=!0}),this.emitter.listenDOM("mouseup",document.body,function(){E.mouseDown=!1,E.update(u.default.sources.USER)})}},{key:"focus",value:function(){this.hasFocus()||(this.root.focus(),this.setRange(this.savedRange))}},{key:"format",value:function(E,d){if(!(this.scroll.whitelist!=null&&!this.scroll.whitelist[E])){this.scroll.update();var _=this.getNativeRange();if(!(_==null||!_.native.collapsed||p.default.query(E,p.default.Scope.BLOCK))){if(_.start.node!==this.cursor.textNode){var N=p.default.find(_.start.node,!1);if(N==null)return;if(N instanceof p.default.Leaf){var x=N.split(_.start.offset);N.parent.insertBefore(this.cursor,x)}else N.insertBefore(this.cursor,_.start.node);this.cursor.attach()}this.cursor.format(E,d),this.scroll.optimize(),this.setNativeRange(this.cursor.textNode,this.cursor.textNode.data.length),this.update()}}}},{key:"getBounds",value:function(E){var d=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0,_=this.scroll.length();E=Math.min(E,_-1),d=Math.min(E+d,_-1)-E;var N=void 0,x=this.scroll.leaf(E),R=O(x,2),F=R[0],U=R[1];if(F==null)return null;var W=F.position(U,!0),D=O(W,2);N=D[0],U=D[1];var M=document.createRange();if(d>0){M.setStart(N,U);var T=this.scroll.leaf(E+d),q=O(T,2);if(F=q[0],U=q[1],F==null)return null;var j=F.position(U,!0),H=O(j,2);return N=H[0],U=H[1],M.setEnd(N,U),M.getBoundingClientRect()}else{var C="left",P=void 0;return N instanceof Text?(U<N.data.length?(M.setStart(N,U),M.setEnd(N,U+1)):(M.setStart(N,U-1),M.setEnd(N,U),C="right"),P=M.getBoundingClientRect()):(P=F.domNode.getBoundingClientRect(),U>0&&(C="right")),{bottom:P.top+P.height,height:P.height,left:P[C],right:P[C],top:P.top,width:0}}}},{key:"getNativeRange",value:function(){var E=document.getSelection();if(E==null||E.rangeCount<=0)return null;var d=E.getRangeAt(0);if(d==null)return null;var _=this.normalizeNative(d);return n.info("getNativeRange",_),_}},{key:"getRange",value:function(){var E=this.getNativeRange();if(E==null)return[null,null];var d=this.normalizedToRange(E);return[d,E]}},{key:"hasFocus",value:function(){return document.activeElement===this.root}},{key:"normalizedToRange",value:function(E){var d=this,_=[[E.start.node,E.start.offset]];E.native.collapsed||_.push([E.end.node,E.end.offset]);var N=_.map(function(F){var U=O(F,2),W=U[0],D=U[1],M=p.default.find(W,!0),T=M.offset(d.scroll);return D===0?T:M instanceof p.default.Container?T+M.length():T+M.index(W,D)}),x=Math.min(Math.max.apply(Math,i(N)),this.scroll.length()-1),R=Math.min.apply(Math,[x].concat(i(N)));return new h(R,x-R)}},{key:"normalizeNative",value:function(E){if(!A(this.root,E.startContainer)||!E.collapsed&&!A(this.root,E.endContainer))return null;var d={start:{node:E.startContainer,offset:E.startOffset},end:{node:E.endContainer,offset:E.endOffset},native:E};return[d.start,d.end].forEach(function(_){for(var N=_.node,x=_.offset;!(N instanceof Text)&&N.childNodes.length>0;)if(N.childNodes.length>x)N=N.childNodes[x],x=0;else if(N.childNodes.length===x)N=N.lastChild,x=N instanceof Text?N.data.length:N.childNodes.length+1;else break;_.node=N,_.offset=x}),d}},{key:"rangeToNative",value:function(E){var d=this,_=E.collapsed?[E.index]:[E.index,E.index+E.length],N=[],x=this.scroll.length();return _.forEach(function(R,F){R=Math.min(x-1,R);var U=void 0,W=d.scroll.leaf(R),D=O(W,2),M=D[0],T=D[1],q=M.position(T,F!==0),j=O(q,2);U=j[0],T=j[1],N.push(U,T)}),N.length<2&&(N=N.concat(N)),N}},{key:"scrollIntoView",value:function(E){var d=this.lastRange;if(d!=null){var _=this.getBounds(d.index,d.length);if(_!=null){var N=this.scroll.length()-1,x=this.scroll.line(Math.min(d.index,N)),R=O(x,1),F=R[0],U=F;if(d.length>0){var W=this.scroll.line(Math.min(d.index+d.length,N)),D=O(W,1);U=D[0]}if(!(F==null||U==null)){var M=E.getBoundingClientRect();_.top<M.top?E.scrollTop-=M.top-_.top:_.bottom>M.bottom&&(E.scrollTop+=_.bottom-M.bottom)}}}}},{key:"setNativeRange",value:function(E,d){var _=arguments.length>2&&arguments[2]!==void 0?arguments[2]:E,N=arguments.length>3&&arguments[3]!==void 0?arguments[3]:d,x=arguments.length>4&&arguments[4]!==void 0?arguments[4]:!1;if(n.info("setNativeRange",E,d,_,N),!(E!=null&&(this.root.parentNode==null||E.parentNode==null||_.parentNode==null))){var R=document.getSelection();if(R!=null)if(E!=null){this.hasFocus()||this.root.focus();var F=(this.getNativeRange()||{}).native;if(F==null||x||E!==F.startContainer||d!==F.startOffset||_!==F.endContainer||N!==F.endOffset){E.tagName=="BR"&&(d=[].indexOf.call(E.parentNode.childNodes,E),E=E.parentNode),_.tagName=="BR"&&(N=[].indexOf.call(_.parentNode.childNodes,_),_=_.parentNode);var U=document.createRange();U.setStart(E,d),U.setEnd(_,N),R.removeAllRanges(),R.addRange(U)}}else R.removeAllRanges(),this.root.blur(),document.body.focus()}}},{key:"setRange",value:function(E){var d=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1,_=arguments.length>2&&arguments[2]!==void 0?arguments[2]:u.default.sources.API;if(typeof d=="string"&&(_=d,d=!1),n.info("setRange",E),E!=null){var N=this.rangeToNative(E);this.setNativeRange.apply(this,i(N).concat([d]))}else this.setNativeRange(null);this.update(_)}},{key:"update",value:function(){var E=arguments.length>0&&arguments[0]!==void 0?arguments[0]:u.default.sources.USER,d=this.lastRange,_=this.getRange(),N=O(_,2),x=N[0],R=N[1];if(this.lastRange=x,this.lastRange!=null&&(this.savedRange=this.lastRange),!(0,e.default)(d,this.lastRange)){var F;!this.composing&&R!=null&&R.native.collapsed&&R.start.node!==this.cursor.textNode&&this.cursor.restore();var U=[u.default.events.SELECTION_CHANGE,(0,s.default)(this.lastRange),(0,s.default)(d),E];if((F=this.emitter).emit.apply(F,[u.default.events.EDITOR_CHANGE].concat(U)),E!==u.default.sources.SILENT){var W;(W=this.emitter).emit.apply(W,U)}}}}]),k}();function A(k,S){try{S.parentNode}catch{return!1}return S instanceof Text&&(S=S.parentNode),k.contains(S)}v.Range=h,v.default=w},function(g,v,c){Object.defineProperty(v,"__esModule",{value:!0});var O=function(){function u(l,a){for(var r=0;r<a.length;r++){var i=a[r];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(l,i.key,i)}}return function(l,a,r){return a&&u(l.prototype,a),r&&u(l,r),l}}(),m=function u(l,a,r){l===null&&(l=Function.prototype);var i=Object.getOwnPropertyDescriptor(l,a);if(i===void 0){var f=Object.getPrototypeOf(l);return f===null?void 0:u(f,a,r)}else{if("value"in i)return i.value;var n=i.get;return n===void 0?void 0:n.call(r)}},b=c(0),p=y(b);function y(u){return u&&u.__esModule?u:{default:u}}function s(u,l){if(!(u instanceof l))throw new TypeError("Cannot call a class as a function")}function o(u,l){if(!u)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return l&&(typeof l=="object"||typeof l=="function")?l:u}function e(u,l){if(typeof l!="function"&&l!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof l);u.prototype=Object.create(l&&l.prototype,{constructor:{value:u,enumerable:!1,writable:!0,configurable:!0}}),l&&(Object.setPrototypeOf?Object.setPrototypeOf(u,l):u.__proto__=l)}var t=function(u){e(l,u);function l(){return s(this,l),o(this,(l.__proto__||Object.getPrototypeOf(l)).apply(this,arguments))}return O(l,[{key:"insertInto",value:function(r,i){r.children.length===0?m(l.prototype.__proto__||Object.getPrototypeOf(l.prototype),"insertInto",this).call(this,r,i):this.remove()}},{key:"length",value:function(){return 0}},{key:"value",value:function(){return""}}],[{key:"value",value:function(){}}]),l}(p.default.Embed);t.blotName="break",t.tagName="BR",v.default=t},function(g,v,c){var O=this&&this.__extends||function(){var o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var u in t)t.hasOwnProperty(u)&&(e[u]=t[u])};return function(e,t){o(e,t);function u(){this.constructor=e}e.prototype=t===null?Object.create(t):(u.prototype=t.prototype,new u)}}();Object.defineProperty(v,"__esModule",{value:!0});var m=c(44),b=c(30),p=c(1),y=function(o){O(e,o);function e(t){var u=o.call(this,t)||this;return u.build(),u}return e.prototype.appendChild=function(t){this.insertBefore(t)},e.prototype.attach=function(){o.prototype.attach.call(this),this.children.forEach(function(t){t.attach()})},e.prototype.build=function(){var t=this;this.children=new m.default,[].slice.call(this.domNode.childNodes).reverse().forEach(function(u){try{var l=s(u);t.insertBefore(l,t.children.head||void 0)}catch(a){if(a instanceof p.ParchmentError)return;throw a}})},e.prototype.deleteAt=function(t,u){if(t===0&&u===this.length())return this.remove();this.children.forEachAt(t,u,function(l,a,r){l.deleteAt(a,r)})},e.prototype.descendant=function(t,u){var l=this.children.find(u),a=l[0],r=l[1];return t.blotName==null&&t(a)||t.blotName!=null&&a instanceof t?[a,r]:a instanceof e?a.descendant(t,r):[null,-1]},e.prototype.descendants=function(t,u,l){u===void 0&&(u=0),l===void 0&&(l=Number.MAX_VALUE);var a=[],r=l;return this.children.forEachAt(u,l,function(i,f,n){(t.blotName==null&&t(i)||t.blotName!=null&&i instanceof t)&&a.push(i),i instanceof e&&(a=a.concat(i.descendants(t,f,r))),r-=n}),a},e.prototype.detach=function(){this.children.forEach(function(t){t.detach()}),o.prototype.detach.call(this)},e.prototype.formatAt=function(t,u,l,a){this.children.forEachAt(t,u,function(r,i,f){r.formatAt(i,f,l,a)})},e.prototype.insertAt=function(t,u,l){var a=this.children.find(t),r=a[0],i=a[1];if(r)r.insertAt(i,u,l);else{var f=l==null?p.create("text",u):p.create(u,l);this.appendChild(f)}},e.prototype.insertBefore=function(t,u){if(this.statics.allowedChildren!=null&&!this.statics.allowedChildren.some(function(l){return t instanceof l}))throw new p.ParchmentError("Cannot insert "+t.statics.blotName+" into "+this.statics.blotName);t.insertInto(this,u)},e.prototype.length=function(){return this.children.reduce(function(t,u){return t+u.length()},0)},e.prototype.moveChildren=function(t,u){this.children.forEach(function(l){t.insertBefore(l,u)})},e.prototype.optimize=function(t){if(o.prototype.optimize.call(this,t),this.children.length===0)if(this.statics.defaultChild!=null){var u=p.create(this.statics.defaultChild);this.appendChild(u),u.optimize(t)}else this.remove()},e.prototype.path=function(t,u){u===void 0&&(u=!1);var l=this.children.find(t,u),a=l[0],r=l[1],i=[[this,t]];return a instanceof e?i.concat(a.path(r,u)):(a!=null&&i.push([a,r]),i)},e.prototype.removeChild=function(t){this.children.remove(t)},e.prototype.replace=function(t){t instanceof e&&t.moveChildren(this),o.prototype.replace.call(this,t)},e.prototype.split=function(t,u){if(u===void 0&&(u=!1),!u){if(t===0)return this;if(t===this.length())return this.next}var l=this.clone();return this.parent.insertBefore(l,this.next),this.children.forEachAt(t,this.length(),function(a,r,i){a=a.split(r,u),l.appendChild(a)}),l},e.prototype.unwrap=function(){this.moveChildren(this.parent,this.next),this.remove()},e.prototype.update=function(t,u){var l=this,a=[],r=[];t.forEach(function(i){i.target===l.domNode&&i.type==="childList"&&(a.push.apply(a,i.addedNodes),r.push.apply(r,i.removedNodes))}),r.forEach(function(i){if(!(i.parentNode!=null&&i.tagName!=="IFRAME"&&document.body.compareDocumentPosition(i)&Node.DOCUMENT_POSITION_CONTAINED_BY)){var f=p.find(i);f!=null&&(f.domNode.parentNode==null||f.domNode.parentNode===l.domNode)&&f.detach()}}),a.filter(function(i){return i.parentNode==l.domNode}).sort(function(i,f){return i===f?0:i.compareDocumentPosition(f)&Node.DOCUMENT_POSITION_FOLLOWING?1:-1}).forEach(function(i){var f=null;i.nextSibling!=null&&(f=p.find(i.nextSibling));var n=s(i);(n.next!=f||n.next==null)&&(n.parent!=null&&n.parent.removeChild(l),l.insertBefore(n,f||void 0))})},e}(b.default);function s(o){var e=p.find(o);if(e==null)try{e=p.create(o)}catch{e=p.create(p.Scope.INLINE),[].slice.call(o.childNodes).forEach(function(u){e.domNode.appendChild(u)}),o.parentNode&&o.parentNode.replaceChild(e.domNode,o),e.attach()}return e}v.default=y},function(g,v,c){var O=this&&this.__extends||function(){var o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var u in t)t.hasOwnProperty(u)&&(e[u]=t[u])};return function(e,t){o(e,t);function u(){this.constructor=e}e.prototype=t===null?Object.create(t):(u.prototype=t.prototype,new u)}}();Object.defineProperty(v,"__esModule",{value:!0});var m=c(12),b=c(31),p=c(17),y=c(1),s=function(o){O(e,o);function e(t){var u=o.call(this,t)||this;return u.attributes=new b.default(u.domNode),u}return e.formats=function(t){if(typeof this.tagName=="string")return!0;if(Array.isArray(this.tagName))return t.tagName.toLowerCase()},e.prototype.format=function(t,u){var l=y.query(t);l instanceof m.default?this.attributes.attribute(l,u):u&&l!=null&&(t!==this.statics.blotName||this.formats()[t]!==u)&&this.replaceWith(t,u)},e.prototype.formats=function(){var t=this.attributes.values(),u=this.statics.formats(this.domNode);return u!=null&&(t[this.statics.blotName]=u),t},e.prototype.replaceWith=function(t,u){var l=o.prototype.replaceWith.call(this,t,u);return this.attributes.copy(l),l},e.prototype.update=function(t,u){var l=this;o.prototype.update.call(this,t,u),t.some(function(a){return a.target===l.domNode&&a.type==="attributes"})&&this.attributes.build()},e.prototype.wrap=function(t,u){var l=o.prototype.wrap.call(this,t,u);return l instanceof e&&l.statics.scope===this.statics.scope&&this.attributes.move(l),l},e}(p.default);v.default=s},function(g,v,c){var O=this&&this.__extends||function(){var y=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(s,o){s.__proto__=o}||function(s,o){for(var e in o)o.hasOwnProperty(e)&&(s[e]=o[e])};return function(s,o){y(s,o);function e(){this.constructor=s}s.prototype=o===null?Object.create(o):(e.prototype=o.prototype,new e)}}();Object.defineProperty(v,"__esModule",{value:!0});var m=c(30),b=c(1),p=function(y){O(s,y);function s(){return y!==null&&y.apply(this,arguments)||this}return s.value=function(o){return!0},s.prototype.index=function(o,e){return this.domNode===o||this.domNode.compareDocumentPosition(o)&Node.DOCUMENT_POSITION_CONTAINED_BY?Math.min(e,1):-1},s.prototype.position=function(o,e){var t=[].indexOf.call(this.parent.domNode.childNodes,this.domNode);return o>0&&(t+=1),[this.parent.domNode,t]},s.prototype.value=function(){var o;return o={},o[this.statics.blotName]=this.statics.value(this.domNode)||!0,o},s.scope=b.Scope.INLINE_BLOT,s}(m.default);v.default=p},function(g,v,c){var O=c(11),m=c(3),b={attributes:{compose:function(y,s,o){typeof y!="object"&&(y={}),typeof s!="object"&&(s={});var e=m(!0,{},s);o||(e=Object.keys(e).reduce(function(u,l){return e[l]!=null&&(u[l]=e[l]),u},{}));for(var t in y)y[t]!==void 0&&s[t]===void 0&&(e[t]=y[t]);return Object.keys(e).length>0?e:void 0},diff:function(y,s){typeof y!="object"&&(y={}),typeof s!="object"&&(s={});var o=Object.keys(y).concat(Object.keys(s)).reduce(function(e,t){return O(y[t],s[t])||(e[t]=s[t]===void 0?null:s[t]),e},{});return Object.keys(o).length>0?o:void 0},transform:function(y,s,o){if(typeof y!="object")return s;if(typeof s=="object"){if(!o)return s;var e=Object.keys(s).reduce(function(t,u){return y[u]===void 0&&(t[u]=s[u]),t},{});return Object.keys(e).length>0?e:void 0}}},iterator:function(y){return new p(y)},length:function(y){return typeof y.delete=="number"?y.delete:typeof y.retain=="number"?y.retain:typeof y.insert=="string"?y.insert.length:1}};function p(y){this.ops=y,this.index=0,this.offset=0}p.prototype.hasNext=function(){return this.peekLength()<1/0},p.prototype.next=function(y){y||(y=1/0);var s=this.ops[this.index];if(s){var o=this.offset,e=b.length(s);if(y>=e-o?(y=e-o,this.index+=1,this.offset=0):this.offset+=y,typeof s.delete=="number")return{delete:y};var t={};return s.attributes&&(t.attributes=s.attributes),typeof s.retain=="number"?t.retain=y:typeof s.insert=="string"?t.insert=s.insert.substr(o,y):t.insert=s.insert,t}else return{retain:1/0}},p.prototype.peek=function(){return this.ops[this.index]},p.prototype.peekLength=function(){return this.ops[this.index]?b.length(this.ops[this.index])-this.offset:1/0},p.prototype.peekType=function(){return this.ops[this.index]?typeof this.ops[this.index].delete=="number"?"delete":typeof this.ops[this.index].retain=="number"?"retain":"insert":"retain"},p.prototype.rest=function(){if(this.hasNext()){if(this.offset===0)return this.ops.slice(this.index);var y=this.offset,s=this.index,o=this.next(),e=this.ops.slice(this.index);return this.offset=y,this.index=s,[o].concat(e)}else return[]},g.exports=b},function(g,v){var c=function(){function O(l,a){return a!=null&&l instanceof a}var m;try{m=Map}catch{m=function(){}}var b;try{b=Set}catch{b=function(){}}var p;try{p=Promise}catch{p=function(){}}function y(l,a,r,i,f){typeof a=="object"&&(r=a.depth,i=a.prototype,f=a.includeNonEnumerable,a=a.circular);var n=[],h=[],w=typeof Buffer<"u";typeof a>"u"&&(a=!0),typeof r>"u"&&(r=1/0);function A(k,S){if(k===null)return null;if(S===0)return k;var E,d;if(typeof k!="object")return k;if(O(k,m))E=new m;else if(O(k,b))E=new b;else if(O(k,p))E=new p(function(M,T){k.then(function(q){M(A(q,S-1))},function(q){T(A(q,S-1))})});else if(y.__isArray(k))E=[];else if(y.__isRegExp(k))E=new RegExp(k.source,u(k)),k.lastIndex&&(E.lastIndex=k.lastIndex);else if(y.__isDate(k))E=new Date(k.getTime());else{if(w&&Buffer.isBuffer(k))return Buffer.allocUnsafe?E=Buffer.allocUnsafe(k.length):E=new Buffer(k.length),k.copy(E),E;O(k,Error)?E=Object.create(k):typeof i>"u"?(d=Object.getPrototypeOf(k),E=Object.create(d)):(E=Object.create(i),d=i)}if(a){var _=n.indexOf(k);if(_!=-1)return h[_];n.push(k),h.push(E)}O(k,m)&&k.forEach(function(M,T){var q=A(T,S-1),j=A(M,S-1);E.set(q,j)}),O(k,b)&&k.forEach(function(M){var T=A(M,S-1);E.add(T)});for(var N in k){var x;d&&(x=Object.getOwnPropertyDescriptor(d,N)),!(x&&x.set==null)&&(E[N]=A(k[N],S-1))}if(Object.getOwnPropertySymbols)for(var R=Object.getOwnPropertySymbols(k),N=0;N<R.length;N++){var F=R[N],U=Object.getOwnPropertyDescriptor(k,F);U&&!U.enumerable&&!f||(E[F]=A(k[F],S-1),U.enumerable||Object.defineProperty(E,F,{enumerable:!1}))}if(f)for(var W=Object.getOwnPropertyNames(k),N=0;N<W.length;N++){var D=W[N],U=Object.getOwnPropertyDescriptor(k,D);U&&U.enumerable||(E[D]=A(k[D],S-1),Object.defineProperty(E,D,{enumerable:!1}))}return E}return A(l,r)}y.clonePrototype=function(a){if(a===null)return null;var r=function(){};return r.prototype=a,new r};function s(l){return Object.prototype.toString.call(l)}y.__objToStr=s;function o(l){return typeof l=="object"&&s(l)==="[object Date]"}y.__isDate=o;function e(l){return typeof l=="object"&&s(l)==="[object Array]"}y.__isArray=e;function t(l){return typeof l=="object"&&s(l)==="[object RegExp]"}y.__isRegExp=t;function u(l){var a="";return l.global&&(a+="g"),l.ignoreCase&&(a+="i"),l.multiline&&(a+="m"),a}return y.__getRegExpFlags=u,y}();typeof g=="object"&&g.exports&&(g.exports=c)},function(g,v,c){Object.defineProperty(v,"__esModule",{value:!0});var O=function(){function E(d,_){var N=[],x=!0,R=!1,F=void 0;try{for(var U=d[Symbol.iterator](),W;!(x=(W=U.next()).done)&&(N.push(W.value),!(_&&N.length===_));x=!0);}catch(D){R=!0,F=D}finally{try{!x&&U.return&&U.return()}finally{if(R)throw F}}return N}return function(d,_){if(Array.isArray(d))return d;if(Symbol.iterator in Object(d))return E(d,_);throw new TypeError("Invalid attempt to destructure non-iterable instance")}}(),m=function(){function E(d,_){for(var N=0;N<_.length;N++){var x=_[N];x.enumerable=x.enumerable||!1,x.configurable=!0,"value"in x&&(x.writable=!0),Object.defineProperty(d,x.key,x)}}return function(d,_,N){return _&&E(d.prototype,_),N&&E(d,N),d}}(),b=function E(d,_,N){d===null&&(d=Function.prototype);var x=Object.getOwnPropertyDescriptor(d,_);if(x===void 0){var R=Object.getPrototypeOf(d);return R===null?void 0:E(R,_,N)}else{if("value"in x)return x.value;var F=x.get;return F===void 0?void 0:F.call(N)}},p=c(0),y=n(p),s=c(8),o=n(s),e=c(4),t=n(e),u=c(16),l=n(u),a=c(13),r=n(a),i=c(25),f=n(i);function n(E){return E&&E.__esModule?E:{default:E}}function h(E,d){if(!(E instanceof d))throw new TypeError("Cannot call a class as a function")}function w(E,d){if(!E)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return d&&(typeof d=="object"||typeof d=="function")?d:E}function A(E,d){if(typeof d!="function"&&d!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof d);E.prototype=Object.create(d&&d.prototype,{constructor:{value:E,enumerable:!1,writable:!0,configurable:!0}}),d&&(Object.setPrototypeOf?Object.setPrototypeOf(E,d):E.__proto__=d)}function k(E){return E instanceof t.default||E instanceof e.BlockEmbed}var S=function(E){A(d,E);function d(_,N){h(this,d);var x=w(this,(d.__proto__||Object.getPrototypeOf(d)).call(this,_));return x.emitter=N.emitter,Array.isArray(N.whitelist)&&(x.whitelist=N.whitelist.reduce(function(R,F){return R[F]=!0,R},{})),x.domNode.addEventListener("DOMNodeInserted",function(){}),x.optimize(),x.enable(),x}return m(d,[{key:"batchStart",value:function(){this.batch=!0}},{key:"batchEnd",value:function(){this.batch=!1,this.optimize()}},{key:"deleteAt",value:function(N,x){var R=this.line(N),F=O(R,2),U=F[0],W=F[1],D=this.line(N+x),M=O(D,1),T=M[0];if(b(d.prototype.__proto__||Object.getPrototypeOf(d.prototype),"deleteAt",this).call(this,N,x),T!=null&&U!==T&&W>0){if(U instanceof e.BlockEmbed||T instanceof e.BlockEmbed){this.optimize();return}if(U instanceof r.default){var q=U.newlineIndex(U.length(),!0);if(q>-1&&(U=U.split(q+1),U===T)){this.optimize();return}}else if(T instanceof r.default){var j=T.newlineIndex(0);j>-1&&T.split(j+1)}var H=T.children.head instanceof l.default?null:T.children.head;U.moveChildren(T,H),U.remove()}this.optimize()}},{key:"enable",value:function(){var N=arguments.length>0&&arguments[0]!==void 0?arguments[0]:!0;this.domNode.setAttribute("contenteditable",N)}},{key:"formatAt",value:function(N,x,R,F){this.whitelist!=null&&!this.whitelist[R]||(b(d.prototype.__proto__||Object.getPrototypeOf(d.prototype),"formatAt",this).call(this,N,x,R,F),this.optimize())}},{key:"insertAt",value:function(N,x,R){if(!(R!=null&&this.whitelist!=null&&!this.whitelist[x])){if(N>=this.length())if(R==null||y.default.query(x,y.default.Scope.BLOCK)==null){var F=y.default.create(this.statics.defaultChild);this.appendChild(F),R==null&&x.endsWith(`
`)&&(x=x.slice(0,-1)),F.insertAt(0,x,R)}else{var U=y.default.create(x,R);this.appendChild(U)}else b(d.prototype.__proto__||Object.getPrototypeOf(d.prototype),"insertAt",this).call(this,N,x,R);this.optimize()}}},{key:"insertBefore",value:function(N,x){if(N.statics.scope===y.default.Scope.INLINE_BLOT){var R=y.default.create(this.statics.defaultChild);R.appendChild(N),N=R}b(d.prototype.__proto__||Object.getPrototypeOf(d.prototype),"insertBefore",this).call(this,N,x)}},{key:"leaf",value:function(N){return this.path(N).pop()||[null,-1]}},{key:"line",value:function(N){return N===this.length()?this.line(N-1):this.descendant(k,N)}},{key:"lines",value:function(){var N=arguments.length>0&&arguments[0]!==void 0?arguments[0]:0,x=arguments.length>1&&arguments[1]!==void 0?arguments[1]:Number.MAX_VALUE,R=function F(U,W,D){var M=[],T=D;return U.children.forEachAt(W,D,function(q,j,H){k(q)?M.push(q):q instanceof y.default.Container&&(M=M.concat(F(q,j,T))),T-=H}),M};return R(this,N,x)}},{key:"optimize",value:function(){var N=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[],x=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};this.batch!==!0&&(b(d.prototype.__proto__||Object.getPrototypeOf(d.prototype),"optimize",this).call(this,N,x),N.length>0&&this.emitter.emit(o.default.events.SCROLL_OPTIMIZE,N,x))}},{key:"path",value:function(N){return b(d.prototype.__proto__||Object.getPrototypeOf(d.prototype),"path",this).call(this,N).slice(1)}},{key:"update",value:function(N){if(this.batch!==!0){var x=o.default.sources.USER;typeof N=="string"&&(x=N),Array.isArray(N)||(N=this.observer.takeRecords()),N.length>0&&this.emitter.emit(o.default.events.SCROLL_BEFORE_UPDATE,x,N),b(d.prototype.__proto__||Object.getPrototypeOf(d.prototype),"update",this).call(this,N.concat([])),N.length>0&&this.emitter.emit(o.default.events.SCROLL_UPDATE,x,N)}}}]),d}(y.default.Scroll);S.blotName="scroll",S.className="ql-editor",S.tagName="DIV",S.defaultChild="block",S.allowedChildren=[t.default,e.BlockEmbed,f.default],v.default=S},function(g,v,c){Object.defineProperty(v,"__esModule",{value:!0}),v.SHORTKEY=v.default=void 0;var O=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(P){return typeof P}:function(P){return P&&typeof Symbol=="function"&&P.constructor===Symbol&&P!==Symbol.prototype?"symbol":typeof P},m=function(){function P(I,z){var K=[],Z=!0,X=!1,J=void 0;try{for(var ee=I[Symbol.iterator](),ie;!(Z=(ie=ee.next()).done)&&(K.push(ie.value),!(z&&K.length===z));Z=!0);}catch(oe){X=!0,J=oe}finally{try{!Z&&ee.return&&ee.return()}finally{if(X)throw J}}return K}return function(I,z){if(Array.isArray(I))return I;if(Symbol.iterator in Object(I))return P(I,z);throw new TypeError("Invalid attempt to destructure non-iterable instance")}}(),b=function(){function P(I,z){for(var K=0;K<z.length;K++){var Z=z[K];Z.enumerable=Z.enumerable||!1,Z.configurable=!0,"value"in Z&&(Z.writable=!0),Object.defineProperty(I,Z.key,Z)}}return function(I,z,K){return z&&P(I.prototype,z),K&&P(I,K),I}}(),p=c(21),y=E(p),s=c(11),o=E(s),e=c(3),t=E(e),u=c(2),l=E(u),a=c(20),r=E(a),i=c(0),f=E(i),n=c(5),h=E(n),w=c(10),A=E(w),k=c(9),S=E(k);function E(P){return P&&P.__esModule?P:{default:P}}function d(P,I,z){return I in P?Object.defineProperty(P,I,{value:z,enumerable:!0,configurable:!0,writable:!0}):P[I]=z,P}function _(P,I){if(!(P instanceof I))throw new TypeError("Cannot call a class as a function")}function N(P,I){if(!P)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return I&&(typeof I=="object"||typeof I=="function")?I:P}function x(P,I){if(typeof I!="function"&&I!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof I);P.prototype=Object.create(I&&I.prototype,{constructor:{value:P,enumerable:!1,writable:!0,configurable:!0}}),I&&(Object.setPrototypeOf?Object.setPrototypeOf(P,I):P.__proto__=I)}var R=(0,A.default)("quill:keyboard"),F=/Mac/i.test(navigator.platform)?"metaKey":"ctrlKey",U=function(P){x(I,P),b(I,null,[{key:"match",value:function(K,Z){return Z=C(Z),["altKey","ctrlKey","metaKey","shiftKey"].some(function(X){return!!Z[X]!==K[X]&&Z[X]!==null})?!1:Z.key===(K.which||K.keyCode)}}]);function I(z,K){_(this,I);var Z=N(this,(I.__proto__||Object.getPrototypeOf(I)).call(this,z,K));return Z.bindings={},Object.keys(Z.options.bindings).forEach(function(X){X==="list autofill"&&z.scroll.whitelist!=null&&!z.scroll.whitelist.list||Z.options.bindings[X]&&Z.addBinding(Z.options.bindings[X])}),Z.addBinding({key:I.keys.ENTER,shiftKey:null},q),Z.addBinding({key:I.keys.ENTER,metaKey:null,ctrlKey:null,altKey:null},function(){}),/Firefox/i.test(navigator.userAgent)?(Z.addBinding({key:I.keys.BACKSPACE},{collapsed:!0},D),Z.addBinding({key:I.keys.DELETE},{collapsed:!0},M)):(Z.addBinding({key:I.keys.BACKSPACE},{collapsed:!0,prefix:/^.?$/},D),Z.addBinding({key:I.keys.DELETE},{collapsed:!0,suffix:/^.?$/},M)),Z.addBinding({key:I.keys.BACKSPACE},{collapsed:!1},T),Z.addBinding({key:I.keys.DELETE},{collapsed:!1},T),Z.addBinding({key:I.keys.BACKSPACE,altKey:null,ctrlKey:null,metaKey:null,shiftKey:null},{collapsed:!0,offset:0},D),Z.listen(),Z}return b(I,[{key:"addBinding",value:function(K){var Z=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},X=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{},J=C(K);if(J==null||J.key==null)return R.warn("Attempted to add invalid keyboard binding",J);typeof Z=="function"&&(Z={handler:Z}),typeof X=="function"&&(X={handler:X}),J=(0,t.default)(J,Z,X),this.bindings[J.key]=this.bindings[J.key]||[],this.bindings[J.key].push(J)}},{key:"listen",value:function(){var K=this;this.quill.root.addEventListener("keydown",function(Z){if(!Z.defaultPrevented){var X=Z.which||Z.keyCode,J=(K.bindings[X]||[]).filter(function(ae){return I.match(Z,ae)});if(J.length!==0){var ee=K.quill.getSelection();if(!(ee==null||!K.quill.hasFocus())){var ie=K.quill.getLine(ee.index),oe=m(ie,2),se=oe[0],ue=oe[1],V=K.quill.getLeaf(ee.index),$=m(V,2),Y=$[0],Q=$[1],G=ee.length===0?[Y,Q]:K.quill.getLeaf(ee.index+ee.length),te=m(G,2),ne=te[0],re=te[1],he=Y instanceof f.default.Text?Y.value().slice(0,Q):"",pe=ne instanceof f.default.Text?ne.value().slice(re):"",le={collapsed:ee.length===0,empty:ee.length===0&&se.length()<=1,format:K.quill.getFormat(ee),offset:ue,prefix:he,suffix:pe},Ye=J.some(function(ae){if(ae.collapsed!=null&&ae.collapsed!==le.collapsed||ae.empty!=null&&ae.empty!==le.empty||ae.offset!=null&&ae.offset!==le.offset)return!1;if(Array.isArray(ae.format)){if(ae.format.every(function(ge){return le.format[ge]==null}))return!1}else if(O(ae.format)==="object"&&!Object.keys(ae.format).every(function(ge){return ae.format[ge]===!0?le.format[ge]!=null:ae.format[ge]===!1?le.format[ge]==null:(0,o.default)(ae.format[ge],le.format[ge])}))return!1;return ae.prefix!=null&&!ae.prefix.test(le.prefix)||ae.suffix!=null&&!ae.suffix.test(le.suffix)?!1:ae.handler.call(K,ee,le)!==!0});Ye&&Z.preventDefault()}}}})}}]),I}(S.default);U.keys={BACKSPACE:8,TAB:9,ENTER:13,ESCAPE:27,LEFT:37,UP:38,RIGHT:39,DOWN:40,DELETE:46},U.DEFAULTS={bindings:{bold:H("bold"),italic:H("italic"),underline:H("underline"),indent:{key:U.keys.TAB,format:["blockquote","indent","list"],handler:function(I,z){if(z.collapsed&&z.offset!==0)return!0;this.quill.format("indent","+1",h.default.sources.USER)}},outdent:{key:U.keys.TAB,shiftKey:!0,format:["blockquote","indent","list"],handler:function(I,z){if(z.collapsed&&z.offset!==0)return!0;this.quill.format("indent","-1",h.default.sources.USER)}},"outdent backspace":{key:U.keys.BACKSPACE,collapsed:!0,shiftKey:null,metaKey:null,ctrlKey:null,altKey:null,format:["indent","list"],offset:0,handler:function(I,z){z.format.indent!=null?this.quill.format("indent","-1",h.default.sources.USER):z.format.list!=null&&this.quill.format("list",!1,h.default.sources.USER)}},"indent code-block":j(!0),"outdent code-block":j(!1),"remove tab":{key:U.keys.TAB,shiftKey:!0,collapsed:!0,prefix:/\t$/,handler:function(I){this.quill.deleteText(I.index-1,1,h.default.sources.USER)}},tab:{key:U.keys.TAB,handler:function(I){this.quill.history.cutoff();var z=new l.default().retain(I.index).delete(I.length).insert("	");this.quill.updateContents(z,h.default.sources.USER),this.quill.history.cutoff(),this.quill.setSelection(I.index+1,h.default.sources.SILENT)}},"list empty enter":{key:U.keys.ENTER,collapsed:!0,format:["list"],empty:!0,handler:function(I,z){this.quill.format("list",!1,h.default.sources.USER),z.format.indent&&this.quill.format("indent",!1,h.default.sources.USER)}},"checklist enter":{key:U.keys.ENTER,collapsed:!0,format:{list:"checked"},handler:function(I){var z=this.quill.getLine(I.index),K=m(z,2),Z=K[0],X=K[1],J=(0,t.default)({},Z.formats(),{list:"checked"}),ee=new l.default().retain(I.index).insert(`
`,J).retain(Z.length()-X-1).retain(1,{list:"unchecked"});this.quill.updateContents(ee,h.default.sources.USER),this.quill.setSelection(I.index+1,h.default.sources.SILENT),this.quill.scrollIntoView()}},"header enter":{key:U.keys.ENTER,collapsed:!0,format:["header"],suffix:/^$/,handler:function(I,z){var K=this.quill.getLine(I.index),Z=m(K,2),X=Z[0],J=Z[1],ee=new l.default().retain(I.index).insert(`
`,z.format).retain(X.length()-J-1).retain(1,{header:null});this.quill.updateContents(ee,h.default.sources.USER),this.quill.setSelection(I.index+1,h.default.sources.SILENT),this.quill.scrollIntoView()}},"list autofill":{key:" ",collapsed:!0,format:{list:!1},prefix:/^\s*?(\d+\.|-|\*|\[ ?\]|\[x\])$/,handler:function(I,z){var K=z.prefix.length,Z=this.quill.getLine(I.index),X=m(Z,2),J=X[0],ee=X[1];if(ee>K)return!0;var ie=void 0;switch(z.prefix.trim()){case"[]":case"[ ]":ie="unchecked";break;case"[x]":ie="checked";break;case"-":case"*":ie="bullet";break;default:ie="ordered"}this.quill.insertText(I.index," ",h.default.sources.USER),this.quill.history.cutoff();var oe=new l.default().retain(I.index-ee).delete(K+1).retain(J.length()-2-ee).retain(1,{list:ie});this.quill.updateContents(oe,h.default.sources.USER),this.quill.history.cutoff(),this.quill.setSelection(I.index-K,h.default.sources.SILENT)}},"code exit":{key:U.keys.ENTER,collapsed:!0,format:["code-block"],prefix:/\n\n$/,suffix:/^\s+$/,handler:function(I){var z=this.quill.getLine(I.index),K=m(z,2),Z=K[0],X=K[1],J=new l.default().retain(I.index+Z.length()-X-2).retain(1,{"code-block":null}).delete(1);this.quill.updateContents(J,h.default.sources.USER)}},"embed left":W(U.keys.LEFT,!1),"embed left shift":W(U.keys.LEFT,!0),"embed right":W(U.keys.RIGHT,!1),"embed right shift":W(U.keys.RIGHT,!0)}};function W(P,I){var z,K=P===U.keys.LEFT?"prefix":"suffix";return z={key:P,shiftKey:I,altKey:null},d(z,K,/^$/),d(z,"handler",function(X){var J=X.index;P===U.keys.RIGHT&&(J+=X.length+1);var ee=this.quill.getLeaf(J),ie=m(ee,1),oe=ie[0];return oe instanceof f.default.Embed?(P===U.keys.LEFT?I?this.quill.setSelection(X.index-1,X.length+1,h.default.sources.USER):this.quill.setSelection(X.index-1,h.default.sources.USER):I?this.quill.setSelection(X.index,X.length+1,h.default.sources.USER):this.quill.setSelection(X.index+X.length+1,h.default.sources.USER),!1):!0}),z}function D(P,I){if(!(P.index===0||this.quill.getLength()<=1)){var z=this.quill.getLine(P.index),K=m(z,1),Z=K[0],X={};if(I.offset===0){var J=this.quill.getLine(P.index-1),ee=m(J,1),ie=ee[0];if(ie!=null&&ie.length()>1){var oe=Z.formats(),se=this.quill.getFormat(P.index-1,1);X=r.default.attributes.diff(oe,se)||{}}}var ue=/[\uD800-\uDBFF][\uDC00-\uDFFF]$/.test(I.prefix)?2:1;this.quill.deleteText(P.index-ue,ue,h.default.sources.USER),Object.keys(X).length>0&&this.quill.formatLine(P.index-ue,ue,X,h.default.sources.USER),this.quill.focus()}}function M(P,I){var z=/^[\uD800-\uDBFF][\uDC00-\uDFFF]/.test(I.suffix)?2:1;if(!(P.index>=this.quill.getLength()-z)){var K={},Z=0,X=this.quill.getLine(P.index),J=m(X,1),ee=J[0];if(I.offset>=ee.length()-1){var ie=this.quill.getLine(P.index+1),oe=m(ie,1),se=oe[0];if(se){var ue=ee.formats(),V=this.quill.getFormat(P.index,1);K=r.default.attributes.diff(ue,V)||{},Z=se.length()}}this.quill.deleteText(P.index,z,h.default.sources.USER),Object.keys(K).length>0&&this.quill.formatLine(P.index+Z-1,z,K,h.default.sources.USER)}}function T(P){var I=this.quill.getLines(P),z={};if(I.length>1){var K=I[0].formats(),Z=I[I.length-1].formats();z=r.default.attributes.diff(Z,K)||{}}this.quill.deleteText(P,h.default.sources.USER),Object.keys(z).length>0&&this.quill.formatLine(P.index,1,z,h.default.sources.USER),this.quill.setSelection(P.index,h.default.sources.SILENT),this.quill.focus()}function q(P,I){var z=this;P.length>0&&this.quill.scroll.deleteAt(P.index,P.length);var K=Object.keys(I.format).reduce(function(Z,X){return f.default.query(X,f.default.Scope.BLOCK)&&!Array.isArray(I.format[X])&&(Z[X]=I.format[X]),Z},{});this.quill.insertText(P.index,`
`,K,h.default.sources.USER),this.quill.setSelection(P.index+1,h.default.sources.SILENT),this.quill.focus(),Object.keys(I.format).forEach(function(Z){K[Z]==null&&(Array.isArray(I.format[Z])||Z!=="link"&&z.quill.format(Z,I.format[Z],h.default.sources.USER))})}function j(P){return{key:U.keys.TAB,shiftKey:!P,format:{"code-block":!0},handler:function(z){var K=f.default.query("code-block"),Z=z.index,X=z.length,J=this.quill.scroll.descendant(K,Z),ee=m(J,2),ie=ee[0],oe=ee[1];if(ie!=null){var se=this.quill.getIndex(ie),ue=ie.newlineIndex(oe,!0)+1,V=ie.newlineIndex(se+oe+X),$=ie.domNode.textContent.slice(ue,V).split(`
`);oe=0,$.forEach(function(Y,Q){P?(ie.insertAt(ue+oe,K.TAB),oe+=K.TAB.length,Q===0?Z+=K.TAB.length:X+=K.TAB.length):Y.startsWith(K.TAB)&&(ie.deleteAt(ue+oe,K.TAB.length),oe-=K.TAB.length,Q===0?Z-=K.TAB.length:X-=K.TAB.length),oe+=Y.length+1}),this.quill.update(h.default.sources.USER),this.quill.setSelection(Z,X,h.default.sources.SILENT)}}}}function H(P){return{key:P[0].toUpperCase(),shortKey:!0,handler:function(z,K){this.quill.format(P,!K.format[P],h.default.sources.USER)}}}function C(P){if(typeof P=="string"||typeof P=="number")return C({key:P});if((typeof P>"u"?"undefined":O(P))==="object"&&(P=(0,y.default)(P,!1)),typeof P.key=="string")if(U.keys[P.key.toUpperCase()]!=null)P.key=U.keys[P.key.toUpperCase()];else if(P.key.length===1)P.key=P.key.toUpperCase().charCodeAt(0);else return null;return P.shortKey&&(P[F]=P.shortKey,delete P.shortKey),P}v.default=U,v.SHORTKEY=F},function(g,v,c){Object.defineProperty(v,"__esModule",{value:!0});var O=function(){function r(i,f){var n=[],h=!0,w=!1,A=void 0;try{for(var k=i[Symbol.iterator](),S;!(h=(S=k.next()).done)&&(n.push(S.value),!(f&&n.length===f));h=!0);}catch(E){w=!0,A=E}finally{try{!h&&k.return&&k.return()}finally{if(w)throw A}}return n}return function(i,f){if(Array.isArray(i))return i;if(Symbol.iterator in Object(i))return r(i,f);throw new TypeError("Invalid attempt to destructure non-iterable instance")}}(),m=function r(i,f,n){i===null&&(i=Function.prototype);var h=Object.getOwnPropertyDescriptor(i,f);if(h===void 0){var w=Object.getPrototypeOf(i);return w===null?void 0:r(w,f,n)}else{if("value"in h)return h.value;var A=h.get;return A===void 0?void 0:A.call(n)}},b=function(){function r(i,f){for(var n=0;n<f.length;n++){var h=f[n];h.enumerable=h.enumerable||!1,h.configurable=!0,"value"in h&&(h.writable=!0),Object.defineProperty(i,h.key,h)}}return function(i,f,n){return f&&r(i.prototype,f),n&&r(i,n),i}}(),p=c(0),y=e(p),s=c(7),o=e(s);function e(r){return r&&r.__esModule?r:{default:r}}function t(r,i){if(!(r instanceof i))throw new TypeError("Cannot call a class as a function")}function u(r,i){if(!r)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return i&&(typeof i=="object"||typeof i=="function")?i:r}function l(r,i){if(typeof i!="function"&&i!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof i);r.prototype=Object.create(i&&i.prototype,{constructor:{value:r,enumerable:!1,writable:!0,configurable:!0}}),i&&(Object.setPrototypeOf?Object.setPrototypeOf(r,i):r.__proto__=i)}var a=function(r){l(i,r),b(i,null,[{key:"value",value:function(){}}]);function i(f,n){t(this,i);var h=u(this,(i.__proto__||Object.getPrototypeOf(i)).call(this,f));return h.selection=n,h.textNode=document.createTextNode(i.CONTENTS),h.domNode.appendChild(h.textNode),h._length=0,h}return b(i,[{key:"detach",value:function(){this.parent!=null&&this.parent.removeChild(this)}},{key:"format",value:function(n,h){if(this._length!==0)return m(i.prototype.__proto__||Object.getPrototypeOf(i.prototype),"format",this).call(this,n,h);for(var w=this,A=0;w!=null&&w.statics.scope!==y.default.Scope.BLOCK_BLOT;)A+=w.offset(w.parent),w=w.parent;w!=null&&(this._length=i.CONTENTS.length,w.optimize(),w.formatAt(A,i.CONTENTS.length,n,h),this._length=0)}},{key:"index",value:function(n,h){return n===this.textNode?0:m(i.prototype.__proto__||Object.getPrototypeOf(i.prototype),"index",this).call(this,n,h)}},{key:"length",value:function(){return this._length}},{key:"position",value:function(){return[this.textNode,this.textNode.data.length]}},{key:"remove",value:function(){m(i.prototype.__proto__||Object.getPrototypeOf(i.prototype),"remove",this).call(this),this.parent=null}},{key:"restore",value:function(){if(!(this.selection.composing||this.parent==null)){var n=this.textNode,h=this.selection.getNativeRange(),w=void 0,A=void 0,k=void 0;if(h!=null&&h.start.node===n&&h.end.node===n){var S=[n,h.start.offset,h.end.offset];w=S[0],A=S[1],k=S[2]}for(;this.domNode.lastChild!=null&&this.domNode.lastChild!==this.textNode;)this.domNode.parentNode.insertBefore(this.domNode.lastChild,this.domNode);if(this.textNode.data!==i.CONTENTS){var E=this.textNode.data.split(i.CONTENTS).join("");this.next instanceof o.default?(w=this.next.domNode,this.next.insertAt(0,E),this.textNode.data=i.CONTENTS):(this.textNode.data=E,this.parent.insertBefore(y.default.create(this.textNode),this),this.textNode=document.createTextNode(i.CONTENTS),this.domNode.appendChild(this.textNode))}if(this.remove(),A!=null){var d=[A,k].map(function(N){return Math.max(0,Math.min(w.data.length,N-1))}),_=O(d,2);return A=_[0],k=_[1],{startNode:w,startOffset:A,endNode:w,endOffset:k}}}}},{key:"update",value:function(n,h){var w=this;if(n.some(function(k){return k.type==="characterData"&&k.target===w.textNode})){var A=this.restore();A&&(h.range=A)}}},{key:"value",value:function(){return""}}]),i}(y.default.Embed);a.blotName="cursor",a.className="ql-cursor",a.tagName="span",a.CONTENTS="\uFEFF",v.default=a},function(g,v,c){Object.defineProperty(v,"__esModule",{value:!0});var O=c(0),m=y(O),b=c(4),p=y(b);function y(u){return u&&u.__esModule?u:{default:u}}function s(u,l){if(!(u instanceof l))throw new TypeError("Cannot call a class as a function")}function o(u,l){if(!u)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return l&&(typeof l=="object"||typeof l=="function")?l:u}function e(u,l){if(typeof l!="function"&&l!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof l);u.prototype=Object.create(l&&l.prototype,{constructor:{value:u,enumerable:!1,writable:!0,configurable:!0}}),l&&(Object.setPrototypeOf?Object.setPrototypeOf(u,l):u.__proto__=l)}var t=function(u){e(l,u);function l(){return s(this,l),o(this,(l.__proto__||Object.getPrototypeOf(l)).apply(this,arguments))}return l}(m.default.Container);t.allowedChildren=[p.default,b.BlockEmbed,t],v.default=t},function(g,v,c){Object.defineProperty(v,"__esModule",{value:!0}),v.ColorStyle=v.ColorClass=v.ColorAttributor=void 0;var O=function(){function a(r,i){for(var f=0;f<i.length;f++){var n=i[f];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(r,n.key,n)}}return function(r,i,f){return i&&a(r.prototype,i),f&&a(r,f),r}}(),m=function a(r,i,f){r===null&&(r=Function.prototype);var n=Object.getOwnPropertyDescriptor(r,i);if(n===void 0){var h=Object.getPrototypeOf(r);return h===null?void 0:a(h,i,f)}else{if("value"in n)return n.value;var w=n.get;return w===void 0?void 0:w.call(f)}},b=c(0),p=y(b);function y(a){return a&&a.__esModule?a:{default:a}}function s(a,r){if(!(a instanceof r))throw new TypeError("Cannot call a class as a function")}function o(a,r){if(!a)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return r&&(typeof r=="object"||typeof r=="function")?r:a}function e(a,r){if(typeof r!="function"&&r!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof r);a.prototype=Object.create(r&&r.prototype,{constructor:{value:a,enumerable:!1,writable:!0,configurable:!0}}),r&&(Object.setPrototypeOf?Object.setPrototypeOf(a,r):a.__proto__=r)}var t=function(a){e(r,a);function r(){return s(this,r),o(this,(r.__proto__||Object.getPrototypeOf(r)).apply(this,arguments))}return O(r,[{key:"value",value:function(f){var n=m(r.prototype.__proto__||Object.getPrototypeOf(r.prototype),"value",this).call(this,f);return n.startsWith("rgb(")?(n=n.replace(/^[^\d]+/,"").replace(/[^\d]+$/,""),"#"+n.split(",").map(function(h){return("00"+parseInt(h).toString(16)).slice(-2)}).join("")):n}}]),r}(p.default.Attributor.Style),u=new p.default.Attributor.Class("color","ql-color",{scope:p.default.Scope.INLINE}),l=new t("color","color",{scope:p.default.Scope.INLINE});v.ColorAttributor=t,v.ColorClass=u,v.ColorStyle=l},function(g,v,c){Object.defineProperty(v,"__esModule",{value:!0}),v.sanitize=v.default=void 0;var O=function(){function l(a,r){for(var i=0;i<r.length;i++){var f=r[i];f.enumerable=f.enumerable||!1,f.configurable=!0,"value"in f&&(f.writable=!0),Object.defineProperty(a,f.key,f)}}return function(a,r,i){return r&&l(a.prototype,r),i&&l(a,i),a}}(),m=function l(a,r,i){a===null&&(a=Function.prototype);var f=Object.getOwnPropertyDescriptor(a,r);if(f===void 0){var n=Object.getPrototypeOf(a);return n===null?void 0:l(n,r,i)}else{if("value"in f)return f.value;var h=f.get;return h===void 0?void 0:h.call(i)}},b=c(6),p=y(b);function y(l){return l&&l.__esModule?l:{default:l}}function s(l,a){if(!(l instanceof a))throw new TypeError("Cannot call a class as a function")}function o(l,a){if(!l)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return a&&(typeof a=="object"||typeof a=="function")?a:l}function e(l,a){if(typeof a!="function"&&a!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof a);l.prototype=Object.create(a&&a.prototype,{constructor:{value:l,enumerable:!1,writable:!0,configurable:!0}}),a&&(Object.setPrototypeOf?Object.setPrototypeOf(l,a):l.__proto__=a)}var t=function(l){e(a,l);function a(){return s(this,a),o(this,(a.__proto__||Object.getPrototypeOf(a)).apply(this,arguments))}return O(a,[{key:"format",value:function(i,f){if(i!==this.statics.blotName||!f)return m(a.prototype.__proto__||Object.getPrototypeOf(a.prototype),"format",this).call(this,i,f);f=this.constructor.sanitize(f),this.domNode.setAttribute("href",f)}}],[{key:"create",value:function(i){var f=m(a.__proto__||Object.getPrototypeOf(a),"create",this).call(this,i);return i=this.sanitize(i),f.setAttribute("href",i),f.setAttribute("rel","noopener noreferrer"),f.setAttribute("target","_blank"),f}},{key:"formats",value:function(i){return i.getAttribute("href")}},{key:"sanitize",value:function(i){return u(i,this.PROTOCOL_WHITELIST)?i:this.SANITIZED_URL}}]),a}(p.default);t.blotName="link",t.tagName="A",t.SANITIZED_URL="about:blank",t.PROTOCOL_WHITELIST=["http","https","mailto","tel"];function u(l,a){var r=document.createElement("a");r.href=l;var i=r.href.slice(0,r.href.indexOf(":"));return a.indexOf(i)>-1}v.default=t,v.sanitize=u},function(g,v,c){Object.defineProperty(v,"__esModule",{value:!0});var O=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(a){return typeof a}:function(a){return a&&typeof Symbol=="function"&&a.constructor===Symbol&&a!==Symbol.prototype?"symbol":typeof a},m=function(){function a(r,i){for(var f=0;f<i.length;f++){var n=i[f];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(r,n.key,n)}}return function(r,i,f){return i&&a(r.prototype,i),f&&a(r,f),r}}(),b=c(23),p=o(b),y=c(107),s=o(y);function o(a){return a&&a.__esModule?a:{default:a}}function e(a,r){if(!(a instanceof r))throw new TypeError("Cannot call a class as a function")}var t=0;function u(a,r){a.setAttribute(r,a.getAttribute(r)!=="true")}var l=function(){function a(r){var i=this;e(this,a),this.select=r,this.container=document.createElement("span"),this.buildPicker(),this.select.style.display="none",this.select.parentNode.insertBefore(this.container,this.select),this.label.addEventListener("mousedown",function(){i.togglePicker()}),this.label.addEventListener("keydown",function(f){switch(f.keyCode){case p.default.keys.ENTER:i.togglePicker();break;case p.default.keys.ESCAPE:i.escape(),f.preventDefault();break}}),this.select.addEventListener("change",this.update.bind(this))}return m(a,[{key:"togglePicker",value:function(){this.container.classList.toggle("ql-expanded"),u(this.label,"aria-expanded"),u(this.options,"aria-hidden")}},{key:"buildItem",value:function(i){var f=this,n=document.createElement("span");return n.tabIndex="0",n.setAttribute("role","button"),n.classList.add("ql-picker-item"),i.hasAttribute("value")&&n.setAttribute("data-value",i.getAttribute("value")),i.textContent&&n.setAttribute("data-label",i.textContent),n.addEventListener("click",function(){f.selectItem(n,!0)}),n.addEventListener("keydown",function(h){switch(h.keyCode){case p.default.keys.ENTER:f.selectItem(n,!0),h.preventDefault();break;case p.default.keys.ESCAPE:f.escape(),h.preventDefault();break}}),n}},{key:"buildLabel",value:function(){var i=document.createElement("span");return i.classList.add("ql-picker-label"),i.innerHTML=s.default,i.tabIndex="0",i.setAttribute("role","button"),i.setAttribute("aria-expanded","false"),this.container.appendChild(i),i}},{key:"buildOptions",value:function(){var i=this,f=document.createElement("span");f.classList.add("ql-picker-options"),f.setAttribute("aria-hidden","true"),f.tabIndex="-1",f.id="ql-picker-options-"+t,t+=1,this.label.setAttribute("aria-controls",f.id),this.options=f,[].slice.call(this.select.options).forEach(function(n){var h=i.buildItem(n);f.appendChild(h),n.selected===!0&&i.selectItem(h)}),this.container.appendChild(f)}},{key:"buildPicker",value:function(){var i=this;[].slice.call(this.select.attributes).forEach(function(f){i.container.setAttribute(f.name,f.value)}),this.container.classList.add("ql-picker"),this.label=this.buildLabel(),this.buildOptions()}},{key:"escape",value:function(){var i=this;this.close(),setTimeout(function(){return i.label.focus()},1)}},{key:"close",value:function(){this.container.classList.remove("ql-expanded"),this.label.setAttribute("aria-expanded","false"),this.options.setAttribute("aria-hidden","true")}},{key:"selectItem",value:function(i){var f=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1,n=this.container.querySelector(".ql-selected");if(i!==n&&(n!=null&&n.classList.remove("ql-selected"),i!=null&&(i.classList.add("ql-selected"),this.select.selectedIndex=[].indexOf.call(i.parentNode.children,i),i.hasAttribute("data-value")?this.label.setAttribute("data-value",i.getAttribute("data-value")):this.label.removeAttribute("data-value"),i.hasAttribute("data-label")?this.label.setAttribute("data-label",i.getAttribute("data-label")):this.label.removeAttribute("data-label"),f))){if(typeof Event=="function")this.select.dispatchEvent(new Event("change"));else if((typeof Event>"u"?"undefined":O(Event))==="object"){var h=document.createEvent("Event");h.initEvent("change",!0,!0),this.select.dispatchEvent(h)}this.close()}}},{key:"update",value:function(){var i=void 0;if(this.select.selectedIndex>-1){var f=this.container.querySelector(".ql-picker-options").children[this.select.selectedIndex];i=this.select.options[this.select.selectedIndex],this.selectItem(f)}else this.selectItem(null);var n=i!=null&&i!==this.select.querySelector("option[selected]");this.label.classList.toggle("ql-active",n)}}]),a}();v.default=l},function(g,v,c){Object.defineProperty(v,"__esModule",{value:!0});var O=c(0),m=R(O),b=c(5),p=R(b),y=c(4),s=R(y),o=c(16),e=R(o),t=c(25),u=R(t),l=c(24),a=R(l),r=c(35),i=R(r),f=c(6),n=R(f),h=c(22),w=R(h),A=c(7),k=R(A),S=c(55),E=R(S),d=c(42),_=R(d),N=c(23),x=R(N);function R(F){return F&&F.__esModule?F:{default:F}}p.default.register({"blots/block":s.default,"blots/block/embed":y.BlockEmbed,"blots/break":e.default,"blots/container":u.default,"blots/cursor":a.default,"blots/embed":i.default,"blots/inline":n.default,"blots/scroll":w.default,"blots/text":k.default,"modules/clipboard":E.default,"modules/history":_.default,"modules/keyboard":x.default}),m.default.register(s.default,e.default,a.default,n.default,w.default,k.default),v.default=p.default},function(g,v,c){Object.defineProperty(v,"__esModule",{value:!0});var O=c(1),m=function(){function b(p){this.domNode=p,this.domNode[O.DATA_KEY]={blot:this}}return Object.defineProperty(b.prototype,"statics",{get:function(){return this.constructor},enumerable:!0,configurable:!0}),b.create=function(p){if(this.tagName==null)throw new O.ParchmentError("Blot definition missing tagName");var y;return Array.isArray(this.tagName)?(typeof p=="string"&&(p=p.toUpperCase(),parseInt(p).toString()===p&&(p=parseInt(p))),typeof p=="number"?y=document.createElement(this.tagName[p-1]):this.tagName.indexOf(p)>-1?y=document.createElement(p):y=document.createElement(this.tagName[0])):y=document.createElement(this.tagName),this.className&&y.classList.add(this.className),y},b.prototype.attach=function(){this.parent!=null&&(this.scroll=this.parent.scroll)},b.prototype.clone=function(){var p=this.domNode.cloneNode(!1);return O.create(p)},b.prototype.detach=function(){this.parent!=null&&this.parent.removeChild(this),delete this.domNode[O.DATA_KEY]},b.prototype.deleteAt=function(p,y){var s=this.isolate(p,y);s.remove()},b.prototype.formatAt=function(p,y,s,o){var e=this.isolate(p,y);if(O.query(s,O.Scope.BLOT)!=null&&o)e.wrap(s,o);else if(O.query(s,O.Scope.ATTRIBUTE)!=null){var t=O.create(this.statics.scope);e.wrap(t),t.format(s,o)}},b.prototype.insertAt=function(p,y,s){var o=s==null?O.create("text",y):O.create(y,s),e=this.split(p);this.parent.insertBefore(o,e)},b.prototype.insertInto=function(p,y){y===void 0&&(y=null),this.parent!=null&&this.parent.children.remove(this);var s=null;p.children.insertBefore(this,y),y!=null&&(s=y.domNode),(this.domNode.parentNode!=p.domNode||this.domNode.nextSibling!=s)&&p.domNode.insertBefore(this.domNode,s),this.parent=p,this.attach()},b.prototype.isolate=function(p,y){var s=this.split(p);return s.split(y),s},b.prototype.length=function(){return 1},b.prototype.offset=function(p){return p===void 0&&(p=this.parent),this.parent==null||this==p?0:this.parent.children.offset(this)+this.parent.offset(p)},b.prototype.optimize=function(p){this.domNode[O.DATA_KEY]!=null&&delete this.domNode[O.DATA_KEY].mutations},b.prototype.remove=function(){this.domNode.parentNode!=null&&this.domNode.parentNode.removeChild(this.domNode),this.detach()},b.prototype.replace=function(p){p.parent!=null&&(p.parent.insertBefore(this,p.next),p.remove())},b.prototype.replaceWith=function(p,y){var s=typeof p=="string"?O.create(p,y):p;return s.replace(this),s},b.prototype.split=function(p,y){return p===0?this:this.next},b.prototype.update=function(p,y){},b.prototype.wrap=function(p,y){var s=typeof p=="string"?O.create(p,y):p;return this.parent!=null&&this.parent.insertBefore(s,this.next),s.appendChild(this),s},b.blotName="abstract",b}();v.default=m},function(g,v,c){Object.defineProperty(v,"__esModule",{value:!0});var O=c(12),m=c(32),b=c(33),p=c(1),y=function(){function s(o){this.attributes={},this.domNode=o,this.build()}return s.prototype.attribute=function(o,e){e?o.add(this.domNode,e)&&(o.value(this.domNode)!=null?this.attributes[o.attrName]=o:delete this.attributes[o.attrName]):(o.remove(this.domNode),delete this.attributes[o.attrName])},s.prototype.build=function(){var o=this;this.attributes={};var e=O.default.keys(this.domNode),t=m.default.keys(this.domNode),u=b.default.keys(this.domNode);e.concat(t).concat(u).forEach(function(l){var a=p.query(l,p.Scope.ATTRIBUTE);a instanceof O.default&&(o.attributes[a.attrName]=a)})},s.prototype.copy=function(o){var e=this;Object.keys(this.attributes).forEach(function(t){var u=e.attributes[t].value(e.domNode);o.format(t,u)})},s.prototype.move=function(o){var e=this;this.copy(o),Object.keys(this.attributes).forEach(function(t){e.attributes[t].remove(e.domNode)}),this.attributes={}},s.prototype.values=function(){var o=this;return Object.keys(this.attributes).reduce(function(e,t){return e[t]=o.attributes[t].value(o.domNode),e},{})},s}();v.default=y},function(g,v,c){var O=this&&this.__extends||function(){var y=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(s,o){s.__proto__=o}||function(s,o){for(var e in o)o.hasOwnProperty(e)&&(s[e]=o[e])};return function(s,o){y(s,o);function e(){this.constructor=s}s.prototype=o===null?Object.create(o):(e.prototype=o.prototype,new e)}}();Object.defineProperty(v,"__esModule",{value:!0});var m=c(12);function b(y,s){var o=y.getAttribute("class")||"";return o.split(/\s+/).filter(function(e){return e.indexOf(s+"-")===0})}var p=function(y){O(s,y);function s(){return y!==null&&y.apply(this,arguments)||this}return s.keys=function(o){return(o.getAttribute("class")||"").split(/\s+/).map(function(e){return e.split("-").slice(0,-1).join("-")})},s.prototype.add=function(o,e){return this.canAdd(o,e)?(this.remove(o),o.classList.add(this.keyName+"-"+e),!0):!1},s.prototype.remove=function(o){var e=b(o,this.keyName);e.forEach(function(t){o.classList.remove(t)}),o.classList.length===0&&o.removeAttribute("class")},s.prototype.value=function(o){var e=b(o,this.keyName)[0]||"",t=e.slice(this.keyName.length+1);return this.canAdd(o,t)?t:""},s}(m.default);v.default=p},function(g,v,c){var O=this&&this.__extends||function(){var y=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(s,o){s.__proto__=o}||function(s,o){for(var e in o)o.hasOwnProperty(e)&&(s[e]=o[e])};return function(s,o){y(s,o);function e(){this.constructor=s}s.prototype=o===null?Object.create(o):(e.prototype=o.prototype,new e)}}();Object.defineProperty(v,"__esModule",{value:!0});var m=c(12);function b(y){var s=y.split("-"),o=s.slice(1).map(function(e){return e[0].toUpperCase()+e.slice(1)}).join("");return s[0]+o}var p=function(y){O(s,y);function s(){return y!==null&&y.apply(this,arguments)||this}return s.keys=function(o){return(o.getAttribute("style")||"").split(";").map(function(e){var t=e.split(":");return t[0].trim()})},s.prototype.add=function(o,e){return this.canAdd(o,e)?(o.style[b(this.keyName)]=e,!0):!1},s.prototype.remove=function(o){o.style[b(this.keyName)]="",o.getAttribute("style")||o.removeAttribute("style")},s.prototype.value=function(o){var e=o.style[b(this.keyName)];return this.canAdd(o,e)?e:""},s}(m.default);v.default=p},function(g,v,c){Object.defineProperty(v,"__esModule",{value:!0});var O=function(){function p(y,s){for(var o=0;o<s.length;o++){var e=s[o];e.enumerable=e.enumerable||!1,e.configurable=!0,"value"in e&&(e.writable=!0),Object.defineProperty(y,e.key,e)}}return function(y,s,o){return s&&p(y.prototype,s),o&&p(y,o),y}}();function m(p,y){if(!(p instanceof y))throw new TypeError("Cannot call a class as a function")}var b=function(){function p(y,s){m(this,p),this.quill=y,this.options=s,this.modules={}}return O(p,[{key:"init",value:function(){var s=this;Object.keys(this.options.modules).forEach(function(o){s.modules[o]==null&&s.addModule(o)})}},{key:"addModule",value:function(s){var o=this.quill.constructor.import("modules/"+s);return this.modules[s]=new o(this.quill,this.options.modules[s]||{}),this.modules[s]}}]),p}();b.DEFAULTS={modules:{}},b.themes={default:b},v.default=b},function(g,v,c){Object.defineProperty(v,"__esModule",{value:!0});var O=function(){function r(i,f){for(var n=0;n<f.length;n++){var h=f[n];h.enumerable=h.enumerable||!1,h.configurable=!0,"value"in h&&(h.writable=!0),Object.defineProperty(i,h.key,h)}}return function(i,f,n){return f&&r(i.prototype,f),n&&r(i,n),i}}(),m=function r(i,f,n){i===null&&(i=Function.prototype);var h=Object.getOwnPropertyDescriptor(i,f);if(h===void 0){var w=Object.getPrototypeOf(i);return w===null?void 0:r(w,f,n)}else{if("value"in h)return h.value;var A=h.get;return A===void 0?void 0:A.call(n)}},b=c(0),p=o(b),y=c(7),s=o(y);function o(r){return r&&r.__esModule?r:{default:r}}function e(r,i){if(!(r instanceof i))throw new TypeError("Cannot call a class as a function")}function t(r,i){if(!r)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return i&&(typeof i=="object"||typeof i=="function")?i:r}function u(r,i){if(typeof i!="function"&&i!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof i);r.prototype=Object.create(i&&i.prototype,{constructor:{value:r,enumerable:!1,writable:!0,configurable:!0}}),i&&(Object.setPrototypeOf?Object.setPrototypeOf(r,i):r.__proto__=i)}var l="\uFEFF",a=function(r){u(i,r);function i(f){e(this,i);var n=t(this,(i.__proto__||Object.getPrototypeOf(i)).call(this,f));return n.contentNode=document.createElement("span"),n.contentNode.setAttribute("contenteditable",!1),[].slice.call(n.domNode.childNodes).forEach(function(h){n.contentNode.appendChild(h)}),n.leftGuard=document.createTextNode(l),n.rightGuard=document.createTextNode(l),n.domNode.appendChild(n.leftGuard),n.domNode.appendChild(n.contentNode),n.domNode.appendChild(n.rightGuard),n}return O(i,[{key:"index",value:function(n,h){return n===this.leftGuard?0:n===this.rightGuard?1:m(i.prototype.__proto__||Object.getPrototypeOf(i.prototype),"index",this).call(this,n,h)}},{key:"restore",value:function(n){var h=void 0,w=void 0,A=n.data.split(l).join("");if(n===this.leftGuard)if(this.prev instanceof s.default){var k=this.prev.length();this.prev.insertAt(k,A),h={startNode:this.prev.domNode,startOffset:k+A.length}}else w=document.createTextNode(A),this.parent.insertBefore(p.default.create(w),this),h={startNode:w,startOffset:A.length};else n===this.rightGuard&&(this.next instanceof s.default?(this.next.insertAt(0,A),h={startNode:this.next.domNode,startOffset:A.length}):(w=document.createTextNode(A),this.parent.insertBefore(p.default.create(w),this.next),h={startNode:w,startOffset:A.length}));return n.data=l,h}},{key:"update",value:function(n,h){var w=this;n.forEach(function(A){if(A.type==="characterData"&&(A.target===w.leftGuard||A.target===w.rightGuard)){var k=w.restore(A.target);k&&(h.range=k)}})}}]),i}(p.default.Embed);v.default=a},function(g,v,c){Object.defineProperty(v,"__esModule",{value:!0}),v.AlignStyle=v.AlignClass=v.AlignAttribute=void 0;var O=c(0),m=b(O);function b(e){return e&&e.__esModule?e:{default:e}}var p={scope:m.default.Scope.BLOCK,whitelist:["right","center","justify"]},y=new m.default.Attributor.Attribute("align","align",p),s=new m.default.Attributor.Class("align","ql-align",p),o=new m.default.Attributor.Style("align","text-align",p);v.AlignAttribute=y,v.AlignClass=s,v.AlignStyle=o},function(g,v,c){Object.defineProperty(v,"__esModule",{value:!0}),v.BackgroundStyle=v.BackgroundClass=void 0;var O=c(0),m=p(O),b=c(26);function p(o){return o&&o.__esModule?o:{default:o}}var y=new m.default.Attributor.Class("background","ql-bg",{scope:m.default.Scope.INLINE}),s=new b.ColorAttributor("background","background-color",{scope:m.default.Scope.INLINE});v.BackgroundClass=y,v.BackgroundStyle=s},function(g,v,c){Object.defineProperty(v,"__esModule",{value:!0}),v.DirectionStyle=v.DirectionClass=v.DirectionAttribute=void 0;var O=c(0),m=b(O);function b(e){return e&&e.__esModule?e:{default:e}}var p={scope:m.default.Scope.BLOCK,whitelist:["rtl"]},y=new m.default.Attributor.Attribute("direction","dir",p),s=new m.default.Attributor.Class("direction","ql-direction",p),o=new m.default.Attributor.Style("direction","direction",p);v.DirectionAttribute=y,v.DirectionClass=s,v.DirectionStyle=o},function(g,v,c){Object.defineProperty(v,"__esModule",{value:!0}),v.FontClass=v.FontStyle=void 0;var O=function(){function r(i,f){for(var n=0;n<f.length;n++){var h=f[n];h.enumerable=h.enumerable||!1,h.configurable=!0,"value"in h&&(h.writable=!0),Object.defineProperty(i,h.key,h)}}return function(i,f,n){return f&&r(i.prototype,f),n&&r(i,n),i}}(),m=function r(i,f,n){i===null&&(i=Function.prototype);var h=Object.getOwnPropertyDescriptor(i,f);if(h===void 0){var w=Object.getPrototypeOf(i);return w===null?void 0:r(w,f,n)}else{if("value"in h)return h.value;var A=h.get;return A===void 0?void 0:A.call(n)}},b=c(0),p=y(b);function y(r){return r&&r.__esModule?r:{default:r}}function s(r,i){if(!(r instanceof i))throw new TypeError("Cannot call a class as a function")}function o(r,i){if(!r)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return i&&(typeof i=="object"||typeof i=="function")?i:r}function e(r,i){if(typeof i!="function"&&i!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof i);r.prototype=Object.create(i&&i.prototype,{constructor:{value:r,enumerable:!1,writable:!0,configurable:!0}}),i&&(Object.setPrototypeOf?Object.setPrototypeOf(r,i):r.__proto__=i)}var t={scope:p.default.Scope.INLINE,whitelist:["serif","monospace"]},u=new p.default.Attributor.Class("font","ql-font",t),l=function(r){e(i,r);function i(){return s(this,i),o(this,(i.__proto__||Object.getPrototypeOf(i)).apply(this,arguments))}return O(i,[{key:"value",value:function(n){return m(i.prototype.__proto__||Object.getPrototypeOf(i.prototype),"value",this).call(this,n).replace(/["']/g,"")}}]),i}(p.default.Attributor.Style),a=new l("font","font-family",t);v.FontStyle=a,v.FontClass=u},function(g,v,c){Object.defineProperty(v,"__esModule",{value:!0}),v.SizeStyle=v.SizeClass=void 0;var O=c(0),m=b(O);function b(s){return s&&s.__esModule?s:{default:s}}var p=new m.default.Attributor.Class("size","ql-size",{scope:m.default.Scope.INLINE,whitelist:["small","large","huge"]}),y=new m.default.Attributor.Style("size","font-size",{scope:m.default.Scope.INLINE,whitelist:["10px","18px","32px"]});v.SizeClass=p,v.SizeStyle=y},function(g,v,c){g.exports={align:{"":c(76),center:c(77),right:c(78),justify:c(79)},background:c(80),blockquote:c(81),bold:c(82),clean:c(83),code:c(58),"code-block":c(58),color:c(84),direction:{"":c(85),rtl:c(86)},float:{center:c(87),full:c(88),left:c(89),right:c(90)},formula:c(91),header:{1:c(92),2:c(93)},italic:c(94),image:c(95),indent:{"+1":c(96),"-1":c(97)},link:c(98),list:{ordered:c(99),bullet:c(100),check:c(101)},script:{sub:c(102),super:c(103)},strike:c(104),underline:c(105),video:c(106)}},function(g,v,c){Object.defineProperty(v,"__esModule",{value:!0}),v.getLastChangeIndex=v.default=void 0;var O=function(){function f(n,h){for(var w=0;w<h.length;w++){var A=h[w];A.enumerable=A.enumerable||!1,A.configurable=!0,"value"in A&&(A.writable=!0),Object.defineProperty(n,A.key,A)}}return function(n,h,w){return h&&f(n.prototype,h),w&&f(n,w),n}}(),m=c(0),b=e(m),p=c(5),y=e(p),s=c(9),o=e(s);function e(f){return f&&f.__esModule?f:{default:f}}function t(f,n){if(!(f instanceof n))throw new TypeError("Cannot call a class as a function")}function u(f,n){if(!f)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return n&&(typeof n=="object"||typeof n=="function")?n:f}function l(f,n){if(typeof n!="function"&&n!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof n);f.prototype=Object.create(n&&n.prototype,{constructor:{value:f,enumerable:!1,writable:!0,configurable:!0}}),n&&(Object.setPrototypeOf?Object.setPrototypeOf(f,n):f.__proto__=n)}var a=function(f){l(n,f);function n(h,w){t(this,n);var A=u(this,(n.__proto__||Object.getPrototypeOf(n)).call(this,h,w));return A.lastRecorded=0,A.ignoreChange=!1,A.clear(),A.quill.on(y.default.events.EDITOR_CHANGE,function(k,S,E,d){k!==y.default.events.TEXT_CHANGE||A.ignoreChange||(!A.options.userOnly||d===y.default.sources.USER?A.record(S,E):A.transform(S))}),A.quill.keyboard.addBinding({key:"Z",shortKey:!0},A.undo.bind(A)),A.quill.keyboard.addBinding({key:"Z",shortKey:!0,shiftKey:!0},A.redo.bind(A)),/Win/i.test(navigator.platform)&&A.quill.keyboard.addBinding({key:"Y",shortKey:!0},A.redo.bind(A)),A}return O(n,[{key:"change",value:function(w,A){if(this.stack[w].length!==0){var k=this.stack[w].pop();this.stack[A].push(k),this.lastRecorded=0,this.ignoreChange=!0,this.quill.updateContents(k[w],y.default.sources.USER),this.ignoreChange=!1;var S=i(k[w]);this.quill.setSelection(S)}}},{key:"clear",value:function(){this.stack={undo:[],redo:[]}}},{key:"cutoff",value:function(){this.lastRecorded=0}},{key:"record",value:function(w,A){if(w.ops.length!==0){this.stack.redo=[];var k=this.quill.getContents().diff(A),S=Date.now();if(this.lastRecorded+this.options.delay>S&&this.stack.undo.length>0){var E=this.stack.undo.pop();k=k.compose(E.undo),w=E.redo.compose(w)}else this.lastRecorded=S;this.stack.undo.push({redo:w,undo:k}),this.stack.undo.length>this.options.maxStack&&this.stack.undo.shift()}}},{key:"redo",value:function(){this.change("redo","undo")}},{key:"transform",value:function(w){this.stack.undo.forEach(function(A){A.undo=w.transform(A.undo,!0),A.redo=w.transform(A.redo,!0)}),this.stack.redo.forEach(function(A){A.undo=w.transform(A.undo,!0),A.redo=w.transform(A.redo,!0)})}},{key:"undo",value:function(){this.change("undo","redo")}}]),n}(o.default);a.DEFAULTS={delay:1e3,maxStack:100,userOnly:!1};function r(f){var n=f.ops[f.ops.length-1];return n==null?!1:n.insert!=null?typeof n.insert=="string"&&n.insert.endsWith(`
`):n.attributes!=null?Object.keys(n.attributes).some(function(h){return b.default.query(h,b.default.Scope.BLOCK)!=null}):!1}function i(f){var n=f.reduce(function(w,A){return w+=A.delete||0,w},0),h=f.length()-n;return r(f)&&(h-=1),h}v.default=a,v.getLastChangeIndex=i},function(g,v,c){Object.defineProperty(v,"__esModule",{value:!0}),v.default=v.BaseTooltip=void 0;var O=function(){function q(j,H){for(var C=0;C<H.length;C++){var P=H[C];P.enumerable=P.enumerable||!1,P.configurable=!0,"value"in P&&(P.writable=!0),Object.defineProperty(j,P.key,P)}}return function(j,H,C){return H&&q(j.prototype,H),C&&q(j,C),j}}(),m=function q(j,H,C){j===null&&(j=Function.prototype);var P=Object.getOwnPropertyDescriptor(j,H);if(P===void 0){var I=Object.getPrototypeOf(j);return I===null?void 0:q(I,H,C)}else{if("value"in P)return P.value;var z=P.get;return z===void 0?void 0:z.call(C)}},b=c(3),p=S(b),y=c(2),s=S(y),o=c(8),e=S(o),t=c(23),u=S(t),l=c(34),a=S(l),r=c(59),i=S(r),f=c(60),n=S(f),h=c(28),w=S(h),A=c(61),k=S(A);function S(q){return q&&q.__esModule?q:{default:q}}function E(q,j){if(!(q instanceof j))throw new TypeError("Cannot call a class as a function")}function d(q,j){if(!q)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return j&&(typeof j=="object"||typeof j=="function")?j:q}function _(q,j){if(typeof j!="function"&&j!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof j);q.prototype=Object.create(j&&j.prototype,{constructor:{value:q,enumerable:!1,writable:!0,configurable:!0}}),j&&(Object.setPrototypeOf?Object.setPrototypeOf(q,j):q.__proto__=j)}var N=[!1,"center","right","justify"],x=["#000000","#e60000","#ff9900","#ffff00","#008a00","#0066cc","#9933ff","#ffffff","#facccc","#ffebcc","#ffffcc","#cce8cc","#cce0f5","#ebd6ff","#bbbbbb","#f06666","#ffc266","#ffff66","#66b966","#66a3e0","#c285ff","#888888","#a10000","#b26b00","#b2b200","#006100","#0047b2","#6b24b2","#444444","#5c0000","#663d00","#666600","#003700","#002966","#3d1466"],R=[!1,"serif","monospace"],F=["1","2","3",!1],U=["small",!1,"large","huge"],W=function(q){_(j,q);function j(H,C){E(this,j);var P=d(this,(j.__proto__||Object.getPrototypeOf(j)).call(this,H,C)),I=function z(K){if(!document.body.contains(H.root))return document.body.removeEventListener("click",z);P.tooltip!=null&&!P.tooltip.root.contains(K.target)&&document.activeElement!==P.tooltip.textbox&&!P.quill.hasFocus()&&P.tooltip.hide(),P.pickers!=null&&P.pickers.forEach(function(Z){Z.container.contains(K.target)||Z.close()})};return H.emitter.listenDOM("click",document.body,I),P}return O(j,[{key:"addModule",value:function(C){var P=m(j.prototype.__proto__||Object.getPrototypeOf(j.prototype),"addModule",this).call(this,C);return C==="toolbar"&&this.extendToolbar(P),P}},{key:"buildButtons",value:function(C,P){C.forEach(function(I){var z=I.getAttribute("class")||"";z.split(/\s+/).forEach(function(K){if(K.startsWith("ql-")&&(K=K.slice(3),P[K]!=null))if(K==="direction")I.innerHTML=P[K][""]+P[K].rtl;else if(typeof P[K]=="string")I.innerHTML=P[K];else{var Z=I.value||"";Z!=null&&P[K][Z]&&(I.innerHTML=P[K][Z])}})})}},{key:"buildPickers",value:function(C,P){var I=this;this.pickers=C.map(function(K){if(K.classList.contains("ql-align"))return K.querySelector("option")==null&&T(K,N),new n.default(K,P.align);if(K.classList.contains("ql-background")||K.classList.contains("ql-color")){var Z=K.classList.contains("ql-background")?"background":"color";return K.querySelector("option")==null&&T(K,x,Z==="background"?"#ffffff":"#000000"),new i.default(K,P[Z])}else return K.querySelector("option")==null&&(K.classList.contains("ql-font")?T(K,R):K.classList.contains("ql-header")?T(K,F):K.classList.contains("ql-size")&&T(K,U)),new w.default(K)});var z=function(){I.pickers.forEach(function(Z){Z.update()})};this.quill.on(e.default.events.EDITOR_CHANGE,z)}}]),j}(a.default);W.DEFAULTS=(0,p.default)(!0,{},a.default.DEFAULTS,{modules:{toolbar:{handlers:{formula:function(){this.quill.theme.tooltip.edit("formula")},image:function(){var j=this,H=this.container.querySelector("input.ql-image[type=file]");H==null&&(H=document.createElement("input"),H.setAttribute("type","file"),H.setAttribute("accept","image/png, image/gif, image/jpeg, image/bmp, image/x-icon"),H.classList.add("ql-image"),H.addEventListener("change",function(){if(H.files!=null&&H.files[0]!=null){var C=new FileReader;C.onload=function(P){var I=j.quill.getSelection(!0);j.quill.updateContents(new s.default().retain(I.index).delete(I.length).insert({image:P.target.result}),e.default.sources.USER),j.quill.setSelection(I.index+1,e.default.sources.SILENT),H.value=""},C.readAsDataURL(H.files[0])}}),this.container.appendChild(H)),H.click()},video:function(){this.quill.theme.tooltip.edit("video")}}}}});var D=function(q){_(j,q);function j(H,C){E(this,j);var P=d(this,(j.__proto__||Object.getPrototypeOf(j)).call(this,H,C));return P.textbox=P.root.querySelector('input[type="text"]'),P.listen(),P}return O(j,[{key:"listen",value:function(){var C=this;this.textbox.addEventListener("keydown",function(P){u.default.match(P,"enter")?(C.save(),P.preventDefault()):u.default.match(P,"escape")&&(C.cancel(),P.preventDefault())})}},{key:"cancel",value:function(){this.hide()}},{key:"edit",value:function(){var C=arguments.length>0&&arguments[0]!==void 0?arguments[0]:"link",P=arguments.length>1&&arguments[1]!==void 0?arguments[1]:null;this.root.classList.remove("ql-hidden"),this.root.classList.add("ql-editing"),P!=null?this.textbox.value=P:C!==this.root.getAttribute("data-mode")&&(this.textbox.value=""),this.position(this.quill.getBounds(this.quill.selection.savedRange)),this.textbox.select(),this.textbox.setAttribute("placeholder",this.textbox.getAttribute("data-"+C)||""),this.root.setAttribute("data-mode",C)}},{key:"restoreFocus",value:function(){var C=this.quill.scrollingContainer.scrollTop;this.quill.focus(),this.quill.scrollingContainer.scrollTop=C}},{key:"save",value:function(){var C=this.textbox.value;switch(this.root.getAttribute("data-mode")){case"link":{var P=this.quill.root.scrollTop;this.linkRange?(this.quill.formatText(this.linkRange,"link",C,e.default.sources.USER),delete this.linkRange):(this.restoreFocus(),this.quill.format("link",C,e.default.sources.USER)),this.quill.root.scrollTop=P;break}case"video":C=M(C);case"formula":{if(!C)break;var I=this.quill.getSelection(!0);if(I!=null){var z=I.index+I.length;this.quill.insertEmbed(z,this.root.getAttribute("data-mode"),C,e.default.sources.USER),this.root.getAttribute("data-mode")==="formula"&&this.quill.insertText(z+1," ",e.default.sources.USER),this.quill.setSelection(z+2,e.default.sources.USER)}break}}this.textbox.value="",this.hide()}}]),j}(k.default);function M(q){var j=q.match(/^(?:(https?):\/\/)?(?:(?:www|m)\.)?youtube\.com\/watch.*v=([a-zA-Z0-9_-]+)/)||q.match(/^(?:(https?):\/\/)?(?:(?:www|m)\.)?youtu\.be\/([a-zA-Z0-9_-]+)/);return j?(j[1]||"https")+"://www.youtube.com/embed/"+j[2]+"?showinfo=0":(j=q.match(/^(?:(https?):\/\/)?(?:www\.)?vimeo\.com\/(\d+)/))?(j[1]||"https")+"://player.vimeo.com/video/"+j[2]+"/":q}function T(q,j){var H=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!1;j.forEach(function(C){var P=document.createElement("option");C===H?P.setAttribute("selected","selected"):P.setAttribute("value",C),q.appendChild(P)})}v.BaseTooltip=D,v.default=W},function(g,v,c){Object.defineProperty(v,"__esModule",{value:!0});var O=function(){function m(){this.head=this.tail=null,this.length=0}return m.prototype.append=function(){for(var b=[],p=0;p<arguments.length;p++)b[p]=arguments[p];this.insertBefore(b[0],null),b.length>1&&this.append.apply(this,b.slice(1))},m.prototype.contains=function(b){for(var p,y=this.iterator();p=y();)if(p===b)return!0;return!1},m.prototype.insertBefore=function(b,p){b&&(b.next=p,p!=null?(b.prev=p.prev,p.prev!=null&&(p.prev.next=b),p.prev=b,p===this.head&&(this.head=b)):this.tail!=null?(this.tail.next=b,b.prev=this.tail,this.tail=b):(b.prev=null,this.head=this.tail=b),this.length+=1)},m.prototype.offset=function(b){for(var p=0,y=this.head;y!=null;){if(y===b)return p;p+=y.length(),y=y.next}return-1},m.prototype.remove=function(b){this.contains(b)&&(b.prev!=null&&(b.prev.next=b.next),b.next!=null&&(b.next.prev=b.prev),b===this.head&&(this.head=b.next),b===this.tail&&(this.tail=b.prev),this.length-=1)},m.prototype.iterator=function(b){return b===void 0&&(b=this.head),function(){var p=b;return b!=null&&(b=b.next),p}},m.prototype.find=function(b,p){p===void 0&&(p=!1);for(var y,s=this.iterator();y=s();){var o=y.length();if(b<o||p&&b===o&&(y.next==null||y.next.length()!==0))return[y,b];b-=o}return[null,0]},m.prototype.forEach=function(b){for(var p,y=this.iterator();p=y();)b(p)},m.prototype.forEachAt=function(b,p,y){if(!(p<=0))for(var s=this.find(b),o=s[0],e=s[1],t,u=b-e,l=this.iterator(o);(t=l())&&u<b+p;){var a=t.length();b>u?y(t,b-u,Math.min(p,u+a-b)):y(t,0,Math.min(a,b+p-u)),u+=a}},m.prototype.map=function(b){return this.reduce(function(p,y){return p.push(b(y)),p},[])},m.prototype.reduce=function(b,p){for(var y,s=this.iterator();y=s();)p=b(p,y);return p},m}();v.default=O},function(g,v,c){var O=this&&this.__extends||function(){var o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var u in t)t.hasOwnProperty(u)&&(e[u]=t[u])};return function(e,t){o(e,t);function u(){this.constructor=e}e.prototype=t===null?Object.create(t):(u.prototype=t.prototype,new u)}}();Object.defineProperty(v,"__esModule",{value:!0});var m=c(17),b=c(1),p={attributes:!0,characterData:!0,characterDataOldValue:!0,childList:!0,subtree:!0},y=100,s=function(o){O(e,o);function e(t){var u=o.call(this,t)||this;return u.scroll=u,u.observer=new MutationObserver(function(l){u.update(l)}),u.observer.observe(u.domNode,p),u.attach(),u}return e.prototype.detach=function(){o.prototype.detach.call(this),this.observer.disconnect()},e.prototype.deleteAt=function(t,u){this.update(),t===0&&u===this.length()?this.children.forEach(function(l){l.remove()}):o.prototype.deleteAt.call(this,t,u)},e.prototype.formatAt=function(t,u,l,a){this.update(),o.prototype.formatAt.call(this,t,u,l,a)},e.prototype.insertAt=function(t,u,l){this.update(),o.prototype.insertAt.call(this,t,u,l)},e.prototype.optimize=function(t,u){var l=this;t===void 0&&(t=[]),u===void 0&&(u={}),o.prototype.optimize.call(this,u);for(var a=[].slice.call(this.observer.takeRecords());a.length>0;)t.push(a.pop());for(var r=function(h,w){w===void 0&&(w=!0),!(h==null||h===l)&&h.domNode.parentNode!=null&&(h.domNode[b.DATA_KEY].mutations==null&&(h.domNode[b.DATA_KEY].mutations=[]),w&&r(h.parent))},i=function(h){h.domNode[b.DATA_KEY]==null||h.domNode[b.DATA_KEY].mutations==null||(h instanceof m.default&&h.children.forEach(i),h.optimize(u))},f=t,n=0;f.length>0;n+=1){if(n>=y)throw new Error("[Parchment] Maximum optimize iterations reached");for(f.forEach(function(h){var w=b.find(h.target,!0);w!=null&&(w.domNode===h.target&&(h.type==="childList"?(r(b.find(h.previousSibling,!1)),[].forEach.call(h.addedNodes,function(A){var k=b.find(A,!1);r(k,!1),k instanceof m.default&&k.children.forEach(function(S){r(S,!1)})})):h.type==="attributes"&&r(w.prev)),r(w))}),this.children.forEach(i),f=[].slice.call(this.observer.takeRecords()),a=f.slice();a.length>0;)t.push(a.pop())}},e.prototype.update=function(t,u){var l=this;u===void 0&&(u={}),t=t||this.observer.takeRecords(),t.map(function(a){var r=b.find(a.target,!0);return r==null?null:r.domNode[b.DATA_KEY].mutations==null?(r.domNode[b.DATA_KEY].mutations=[a],r):(r.domNode[b.DATA_KEY].mutations.push(a),null)}).forEach(function(a){a==null||a===l||a.domNode[b.DATA_KEY]==null||a.update(a.domNode[b.DATA_KEY].mutations||[],u)}),this.domNode[b.DATA_KEY].mutations!=null&&o.prototype.update.call(this,this.domNode[b.DATA_KEY].mutations,u),this.optimize(t,u)},e.blotName="scroll",e.defaultChild="block",e.scope=b.Scope.BLOCK_BLOT,e.tagName="DIV",e}(m.default);v.default=s},function(g,v,c){var O=this&&this.__extends||function(){var s=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(o,e){o.__proto__=e}||function(o,e){for(var t in e)e.hasOwnProperty(t)&&(o[t]=e[t])};return function(o,e){s(o,e);function t(){this.constructor=o}o.prototype=e===null?Object.create(e):(t.prototype=e.prototype,new t)}}();Object.defineProperty(v,"__esModule",{value:!0});var m=c(18),b=c(1);function p(s,o){if(Object.keys(s).length!==Object.keys(o).length)return!1;for(var e in s)if(s[e]!==o[e])return!1;return!0}var y=function(s){O(o,s);function o(){return s!==null&&s.apply(this,arguments)||this}return o.formats=function(e){if(e.tagName!==o.tagName)return s.formats.call(this,e)},o.prototype.format=function(e,t){var u=this;e===this.statics.blotName&&!t?(this.children.forEach(function(l){l instanceof m.default||(l=l.wrap(o.blotName,!0)),u.attributes.copy(l)}),this.unwrap()):s.prototype.format.call(this,e,t)},o.prototype.formatAt=function(e,t,u,l){if(this.formats()[u]!=null||b.query(u,b.Scope.ATTRIBUTE)){var a=this.isolate(e,t);a.format(u,l)}else s.prototype.formatAt.call(this,e,t,u,l)},o.prototype.optimize=function(e){s.prototype.optimize.call(this,e);var t=this.formats();if(Object.keys(t).length===0)return this.unwrap();var u=this.next;u instanceof o&&u.prev===this&&p(t,u.formats())&&(u.moveChildren(this),u.remove())},o.blotName="inline",o.scope=b.Scope.INLINE_BLOT,o.tagName="SPAN",o}(m.default);v.default=y},function(g,v,c){var O=this&&this.__extends||function(){var y=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(s,o){s.__proto__=o}||function(s,o){for(var e in o)o.hasOwnProperty(e)&&(s[e]=o[e])};return function(s,o){y(s,o);function e(){this.constructor=s}s.prototype=o===null?Object.create(o):(e.prototype=o.prototype,new e)}}();Object.defineProperty(v,"__esModule",{value:!0});var m=c(18),b=c(1),p=function(y){O(s,y);function s(){return y!==null&&y.apply(this,arguments)||this}return s.formats=function(o){var e=b.query(s.blotName).tagName;if(o.tagName!==e)return y.formats.call(this,o)},s.prototype.format=function(o,e){b.query(o,b.Scope.BLOCK)!=null&&(o===this.statics.blotName&&!e?this.replaceWith(s.blotName):y.prototype.format.call(this,o,e))},s.prototype.formatAt=function(o,e,t,u){b.query(t,b.Scope.BLOCK)!=null?this.format(t,u):y.prototype.formatAt.call(this,o,e,t,u)},s.prototype.insertAt=function(o,e,t){if(t==null||b.query(e,b.Scope.INLINE)!=null)y.prototype.insertAt.call(this,o,e,t);else{var u=this.split(o),l=b.create(e,t);u.parent.insertBefore(l,u)}},s.prototype.update=function(o,e){navigator.userAgent.match(/Trident/)?this.build():y.prototype.update.call(this,o,e)},s.blotName="block",s.scope=b.Scope.BLOCK_BLOT,s.tagName="P",s}(m.default);v.default=p},function(g,v,c){var O=this&&this.__extends||function(){var p=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(y,s){y.__proto__=s}||function(y,s){for(var o in s)s.hasOwnProperty(o)&&(y[o]=s[o])};return function(y,s){p(y,s);function o(){this.constructor=y}y.prototype=s===null?Object.create(s):(o.prototype=s.prototype,new o)}}();Object.defineProperty(v,"__esModule",{value:!0});var m=c(19),b=function(p){O(y,p);function y(){return p!==null&&p.apply(this,arguments)||this}return y.formats=function(s){},y.prototype.format=function(s,o){p.prototype.formatAt.call(this,0,this.length(),s,o)},y.prototype.formatAt=function(s,o,e,t){s===0&&o===this.length()?this.format(e,t):p.prototype.formatAt.call(this,s,o,e,t)},y.prototype.formats=function(){return this.statics.formats(this.domNode)},y}(m.default);v.default=b},function(g,v,c){var O=this&&this.__extends||function(){var y=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(s,o){s.__proto__=o}||function(s,o){for(var e in o)o.hasOwnProperty(e)&&(s[e]=o[e])};return function(s,o){y(s,o);function e(){this.constructor=s}s.prototype=o===null?Object.create(o):(e.prototype=o.prototype,new e)}}();Object.defineProperty(v,"__esModule",{value:!0});var m=c(19),b=c(1),p=function(y){O(s,y);function s(o){var e=y.call(this,o)||this;return e.text=e.statics.value(e.domNode),e}return s.create=function(o){return document.createTextNode(o)},s.value=function(o){var e=o.data;return e.normalize&&(e=e.normalize()),e},s.prototype.deleteAt=function(o,e){this.domNode.data=this.text=this.text.slice(0,o)+this.text.slice(o+e)},s.prototype.index=function(o,e){return this.domNode===o?e:-1},s.prototype.insertAt=function(o,e,t){t==null?(this.text=this.text.slice(0,o)+e+this.text.slice(o),this.domNode.data=this.text):y.prototype.insertAt.call(this,o,e,t)},s.prototype.length=function(){return this.text.length},s.prototype.optimize=function(o){y.prototype.optimize.call(this,o),this.text=this.statics.value(this.domNode),this.text.length===0?this.remove():this.next instanceof s&&this.next.prev===this&&(this.insertAt(this.length(),this.next.value()),this.next.remove())},s.prototype.position=function(o,e){return[this.domNode,o]},s.prototype.split=function(o,e){if(e===void 0&&(e=!1),!e){if(o===0)return this;if(o===this.length())return this.next}var t=b.create(this.domNode.splitText(o));return this.parent.insertBefore(t,this.next),this.text=this.statics.value(this.domNode),t},s.prototype.update=function(o,e){var t=this;o.some(function(u){return u.type==="characterData"&&u.target===t.domNode})&&(this.text=this.statics.value(this.domNode))},s.prototype.value=function(){return this.text},s.blotName="text",s.scope=b.Scope.INLINE_BLOT,s}(m.default);v.default=p},function(g,v,c){var O=document.createElement("div");if(O.classList.toggle("test-class",!1),O.classList.contains("test-class")){var m=DOMTokenList.prototype.toggle;DOMTokenList.prototype.toggle=function(b,p){return arguments.length>1&&!this.contains(b)==!p?p:m.call(this,b)}}String.prototype.startsWith||(String.prototype.startsWith=function(b,p){return p=p||0,this.substr(p,b.length)===b}),String.prototype.endsWith||(String.prototype.endsWith=function(b,p){var y=this.toString();(typeof p!="number"||!isFinite(p)||Math.floor(p)!==p||p>y.length)&&(p=y.length),p-=b.length;var s=y.indexOf(b,p);return s!==-1&&s===p}),Array.prototype.find||Object.defineProperty(Array.prototype,"find",{value:function(p){if(this===null)throw new TypeError("Array.prototype.find called on null or undefined");if(typeof p!="function")throw new TypeError("predicate must be a function");for(var y=Object(this),s=y.length>>>0,o=arguments[1],e,t=0;t<s;t++)if(e=y[t],p.call(o,e,t,y))return e}}),document.addEventListener("DOMContentLoaded",function(){document.execCommand("enableObjectResizing",!1,!1),document.execCommand("autoUrlDetect",!1,!1)})},function(g,v){var c=-1,O=1,m=0;function b(n,h,w){if(n==h)return n?[[m,n]]:[];(w<0||n.length<w)&&(w=null);var A=o(n,h),k=n.substring(0,A);n=n.substring(A),h=h.substring(A),A=e(n,h);var S=n.substring(n.length-A);n=n.substring(0,n.length-A),h=h.substring(0,h.length-A);var E=p(n,h);return k&&E.unshift([m,k]),S&&E.push([m,S]),u(E),w!=null&&(E=r(E,w)),E=i(E),E}function p(n,h){var w;if(!n)return[[O,h]];if(!h)return[[c,n]];var A=n.length>h.length?n:h,k=n.length>h.length?h:n,S=A.indexOf(k);if(S!=-1)return w=[[O,A.substring(0,S)],[m,k],[O,A.substring(S+k.length)]],n.length>h.length&&(w[0][0]=w[2][0]=c),w;if(k.length==1)return[[c,n],[O,h]];var E=t(n,h);if(E){var d=E[0],_=E[1],N=E[2],x=E[3],R=E[4],F=b(d,N),U=b(_,x);return F.concat([[m,R]],U)}return y(n,h)}function y(n,h){for(var w=n.length,A=h.length,k=Math.ceil((w+A)/2),S=k,E=2*k,d=new Array(E),_=new Array(E),N=0;N<E;N++)d[N]=-1,_[N]=-1;d[S+1]=0,_[S+1]=0;for(var x=w-A,R=x%2!=0,F=0,U=0,W=0,D=0,M=0;M<k;M++){for(var T=-M+F;T<=M-U;T+=2){var q=S+T,j;T==-M||T!=M&&d[q-1]<d[q+1]?j=d[q+1]:j=d[q-1]+1;for(var H=j-T;j<w&&H<A&&n.charAt(j)==h.charAt(H);)j++,H++;if(d[q]=j,j>w)U+=2;else if(H>A)F+=2;else if(R){var C=S+x-T;if(C>=0&&C<E&&_[C]!=-1){var P=w-_[C];if(j>=P)return s(n,h,j,H)}}}for(var I=-M+W;I<=M-D;I+=2){var C=S+I,P;I==-M||I!=M&&_[C-1]<_[C+1]?P=_[C+1]:P=_[C-1]+1;for(var z=P-I;P<w&&z<A&&n.charAt(w-P-1)==h.charAt(A-z-1);)P++,z++;if(_[C]=P,P>w)D+=2;else if(z>A)W+=2;else if(!R){var q=S+x-I;if(q>=0&&q<E&&d[q]!=-1){var j=d[q],H=S+j-q;if(P=w-P,j>=P)return s(n,h,j,H)}}}}return[[c,n],[O,h]]}function s(n,h,w,A){var k=n.substring(0,w),S=h.substring(0,A),E=n.substring(w),d=h.substring(A),_=b(k,S),N=b(E,d);return _.concat(N)}function o(n,h){if(!n||!h||n.charAt(0)!=h.charAt(0))return 0;for(var w=0,A=Math.min(n.length,h.length),k=A,S=0;w<k;)n.substring(S,k)==h.substring(S,k)?(w=k,S=w):A=k,k=Math.floor((A-w)/2+w);return k}function e(n,h){if(!n||!h||n.charAt(n.length-1)!=h.charAt(h.length-1))return 0;for(var w=0,A=Math.min(n.length,h.length),k=A,S=0;w<k;)n.substring(n.length-k,n.length-S)==h.substring(h.length-k,h.length-S)?(w=k,S=w):A=k,k=Math.floor((A-w)/2+w);return k}function t(n,h){var w=n.length>h.length?n:h,A=n.length>h.length?h:n;if(w.length<4||A.length*2<w.length)return null;function k(U,W,D){for(var M=U.substring(D,D+Math.floor(U.length/4)),T=-1,q="",j,H,C,P;(T=W.indexOf(M,T+1))!=-1;){var I=o(U.substring(D),W.substring(T)),z=e(U.substring(0,D),W.substring(0,T));q.length<z+I&&(q=W.substring(T-z,T)+W.substring(T,T+I),j=U.substring(0,D-z),H=U.substring(D+I),C=W.substring(0,T-z),P=W.substring(T+I))}return q.length*2>=U.length?[j,H,C,P,q]:null}var S=k(w,A,Math.ceil(w.length/4)),E=k(w,A,Math.ceil(w.length/2)),d;if(!S&&!E)return null;E?S?d=S[4].length>E[4].length?S:E:d=E:d=S;var _,N,x,R;n.length>h.length?(_=d[0],N=d[1],x=d[2],R=d[3]):(x=d[0],R=d[1],_=d[2],N=d[3]);var F=d[4];return[_,N,x,R,F]}function u(n){n.push([m,""]);for(var h=0,w=0,A=0,k="",S="",E;h<n.length;)switch(n[h][0]){case O:A++,S+=n[h][1],h++;break;case c:w++,k+=n[h][1],h++;break;case m:w+A>1?(w!==0&&A!==0&&(E=o(S,k),E!==0&&(h-w-A>0&&n[h-w-A-1][0]==m?n[h-w-A-1][1]+=S.substring(0,E):(n.splice(0,0,[m,S.substring(0,E)]),h++),S=S.substring(E),k=k.substring(E)),E=e(S,k),E!==0&&(n[h][1]=S.substring(S.length-E)+n[h][1],S=S.substring(0,S.length-E),k=k.substring(0,k.length-E))),w===0?n.splice(h-A,w+A,[O,S]):A===0?n.splice(h-w,w+A,[c,k]):n.splice(h-w-A,w+A,[c,k],[O,S]),h=h-w-A+(w?1:0)+(A?1:0)+1):h!==0&&n[h-1][0]==m?(n[h-1][1]+=n[h][1],n.splice(h,1)):h++,A=0,w=0,k="",S="";break}n[n.length-1][1]===""&&n.pop();var d=!1;for(h=1;h<n.length-1;)n[h-1][0]==m&&n[h+1][0]==m&&(n[h][1].substring(n[h][1].length-n[h-1][1].length)==n[h-1][1]?(n[h][1]=n[h-1][1]+n[h][1].substring(0,n[h][1].length-n[h-1][1].length),n[h+1][1]=n[h-1][1]+n[h+1][1],n.splice(h-1,1),d=!0):n[h][1].substring(0,n[h+1][1].length)==n[h+1][1]&&(n[h-1][1]+=n[h+1][1],n[h][1]=n[h][1].substring(n[h+1][1].length)+n[h+1][1],n.splice(h+1,1),d=!0)),h++;d&&u(n)}var l=b;l.INSERT=O,l.DELETE=c,l.EQUAL=m,g.exports=l;function a(n,h){if(h===0)return[m,n];for(var w=0,A=0;A<n.length;A++){var k=n[A];if(k[0]===c||k[0]===m){var S=w+k[1].length;if(h===S)return[A+1,n];if(h<S){n=n.slice();var E=h-w,d=[k[0],k[1].slice(0,E)],_=[k[0],k[1].slice(E)];return n.splice(A,1,d,_),[A+1,n]}else w=S}}throw new Error("cursor_pos is out of bounds!")}function r(n,h){var w=a(n,h),A=w[1],k=w[0],S=A[k],E=A[k+1];if(S==null)return n;if(S[0]!==m)return n;if(E!=null&&S[1]+E[1]===E[1]+S[1])return A.splice(k,2,E,S),f(A,k,2);if(E!=null&&E[1].indexOf(S[1])===0){A.splice(k,2,[E[0],S[1]],[0,S[1]]);var d=E[1].slice(S[1].length);return d.length>0&&A.splice(k+2,0,[E[0],d]),f(A,k,3)}else return n}function i(n){for(var h=!1,w=function(E){return E.charCodeAt(0)>=56320&&E.charCodeAt(0)<=57343},A=function(E){return E.charCodeAt(E.length-1)>=55296&&E.charCodeAt(E.length-1)<=56319},k=2;k<n.length;k+=1)n[k-2][0]===m&&A(n[k-2][1])&&n[k-1][0]===c&&w(n[k-1][1])&&n[k][0]===O&&w(n[k][1])&&(h=!0,n[k-1][1]=n[k-2][1].slice(-1)+n[k-1][1],n[k][1]=n[k-2][1].slice(-1)+n[k][1],n[k-2][1]=n[k-2][1].slice(0,-1));if(!h)return n;for(var S=[],k=0;k<n.length;k+=1)n[k][1].length>0&&S.push(n[k]);return S}function f(n,h,w){for(var A=h+w-1;A>=0&&A>=h-1;A--)if(A+1<n.length){var k=n[A],S=n[A+1];k[0]===S[1]&&n.splice(A,2,[k[0],k[1]+S[1]])}return n}},function(g,v){v=g.exports=typeof Object.keys=="function"?Object.keys:c,v.shim=c;function c(O){var m=[];for(var b in O)m.push(b);return m}},function(g,v){var c=function(){return Object.prototype.toString.call(arguments)}()=="[object Arguments]";v=g.exports=c?O:m,v.supported=O;function O(b){return Object.prototype.toString.call(b)=="[object Arguments]"}v.unsupported=m;function m(b){return b&&typeof b=="object"&&typeof b.length=="number"&&Object.prototype.hasOwnProperty.call(b,"callee")&&!Object.prototype.propertyIsEnumerable.call(b,"callee")||!1}},function(g,v){var c=Object.prototype.hasOwnProperty,O="~";function m(){}Object.create&&(m.prototype=Object.create(null),new m().__proto__||(O=!1));function b(y,s,o){this.fn=y,this.context=s,this.once=o||!1}function p(){this._events=new m,this._eventsCount=0}p.prototype.eventNames=function(){var s=[],o,e;if(this._eventsCount===0)return s;for(e in o=this._events)c.call(o,e)&&s.push(O?e.slice(1):e);return Object.getOwnPropertySymbols?s.concat(Object.getOwnPropertySymbols(o)):s},p.prototype.listeners=function(s,o){var e=O?O+s:s,t=this._events[e];if(o)return!!t;if(!t)return[];if(t.fn)return[t.fn];for(var u=0,l=t.length,a=new Array(l);u<l;u++)a[u]=t[u].fn;return a},p.prototype.emit=function(s,o,e,t,u,l){var a=O?O+s:s;if(!this._events[a])return!1;var r=this._events[a],i=arguments.length,f,n;if(r.fn){switch(r.once&&this.removeListener(s,r.fn,void 0,!0),i){case 1:return r.fn.call(r.context),!0;case 2:return r.fn.call(r.context,o),!0;case 3:return r.fn.call(r.context,o,e),!0;case 4:return r.fn.call(r.context,o,e,t),!0;case 5:return r.fn.call(r.context,o,e,t,u),!0;case 6:return r.fn.call(r.context,o,e,t,u,l),!0}for(n=1,f=new Array(i-1);n<i;n++)f[n-1]=arguments[n];r.fn.apply(r.context,f)}else{var h=r.length,w;for(n=0;n<h;n++)switch(r[n].once&&this.removeListener(s,r[n].fn,void 0,!0),i){case 1:r[n].fn.call(r[n].context);break;case 2:r[n].fn.call(r[n].context,o);break;case 3:r[n].fn.call(r[n].context,o,e);break;case 4:r[n].fn.call(r[n].context,o,e,t);break;default:if(!f)for(w=1,f=new Array(i-1);w<i;w++)f[w-1]=arguments[w];r[n].fn.apply(r[n].context,f)}}return!0},p.prototype.on=function(s,o,e){var t=new b(o,e||this),u=O?O+s:s;return this._events[u]?this._events[u].fn?this._events[u]=[this._events[u],t]:this._events[u].push(t):(this._events[u]=t,this._eventsCount++),this},p.prototype.once=function(s,o,e){var t=new b(o,e||this,!0),u=O?O+s:s;return this._events[u]?this._events[u].fn?this._events[u]=[this._events[u],t]:this._events[u].push(t):(this._events[u]=t,this._eventsCount++),this},p.prototype.removeListener=function(s,o,e,t){var u=O?O+s:s;if(!this._events[u])return this;if(!o)return--this._eventsCount===0?this._events=new m:delete this._events[u],this;var l=this._events[u];if(l.fn)l.fn===o&&(!t||l.once)&&(!e||l.context===e)&&(--this._eventsCount===0?this._events=new m:delete this._events[u]);else{for(var a=0,r=[],i=l.length;a<i;a++)(l[a].fn!==o||t&&!l[a].once||e&&l[a].context!==e)&&r.push(l[a]);r.length?this._events[u]=r.length===1?r[0]:r:--this._eventsCount===0?this._events=new m:delete this._events[u]}return this},p.prototype.removeAllListeners=function(s){var o;return s?(o=O?O+s:s,this._events[o]&&(--this._eventsCount===0?this._events=new m:delete this._events[o])):(this._events=new m,this._eventsCount=0),this},p.prototype.off=p.prototype.removeListener,p.prototype.addListener=p.prototype.on,p.prototype.setMaxListeners=function(){return this},p.prefixed=O,p.EventEmitter=p,typeof g<"u"&&(g.exports=p)},function(g,v,c){Object.defineProperty(v,"__esModule",{value:!0}),v.matchText=v.matchSpacing=v.matchNewline=v.matchBlot=v.matchAttributor=v.default=void 0;var O=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(V){return typeof V}:function(V){return V&&typeof Symbol=="function"&&V.constructor===Symbol&&V!==Symbol.prototype?"symbol":typeof V},m=function(){function V($,Y){var Q=[],G=!0,te=!1,ne=void 0;try{for(var re=$[Symbol.iterator](),he;!(G=(he=re.next()).done)&&(Q.push(he.value),!(Y&&Q.length===Y));G=!0);}catch(pe){te=!0,ne=pe}finally{try{!G&&re.return&&re.return()}finally{if(te)throw ne}}return Q}return function($,Y){if(Array.isArray($))return $;if(Symbol.iterator in Object($))return V($,Y);throw new TypeError("Invalid attempt to destructure non-iterable instance")}}(),b=function(){function V($,Y){for(var Q=0;Q<Y.length;Q++){var G=Y[Q];G.enumerable=G.enumerable||!1,G.configurable=!0,"value"in G&&(G.writable=!0),Object.defineProperty($,G.key,G)}}return function($,Y,Q){return Y&&V($.prototype,Y),Q&&V($,Q),$}}(),p=c(3),y=_(p),s=c(2),o=_(s),e=c(0),t=_(e),u=c(5),l=_(u),a=c(10),r=_(a),i=c(9),f=_(i),n=c(36),h=c(37),w=c(13),A=_(w),k=c(26),S=c(38),E=c(39),d=c(40);function _(V){return V&&V.__esModule?V:{default:V}}function N(V,$,Y){return $ in V?Object.defineProperty(V,$,{value:Y,enumerable:!0,configurable:!0,writable:!0}):V[$]=Y,V}function x(V,$){if(!(V instanceof $))throw new TypeError("Cannot call a class as a function")}function R(V,$){if(!V)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return $&&(typeof $=="object"||typeof $=="function")?$:V}function F(V,$){if(typeof $!="function"&&$!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof $);V.prototype=Object.create($&&$.prototype,{constructor:{value:V,enumerable:!1,writable:!0,configurable:!0}}),$&&(Object.setPrototypeOf?Object.setPrototypeOf(V,$):V.__proto__=$)}var U=(0,r.default)("quill:clipboard"),W="__ql-matcher",D=[[Node.TEXT_NODE,ue],[Node.TEXT_NODE,ie],["br",X],[Node.ELEMENT_NODE,ie],[Node.ELEMENT_NODE,Z],[Node.ELEMENT_NODE,oe],[Node.ELEMENT_NODE,K],[Node.ELEMENT_NODE,se],["li",ee],["b",z.bind(z,"bold")],["i",z.bind(z,"italic")],["style",J]],M=[n.AlignAttribute,S.DirectionAttribute].reduce(function(V,$){return V[$.keyName]=$,V},{}),T=[n.AlignStyle,h.BackgroundStyle,k.ColorStyle,S.DirectionStyle,E.FontStyle,d.SizeStyle].reduce(function(V,$){return V[$.keyName]=$,V},{}),q=function(V){F($,V);function $(Y,Q){x(this,$);var G=R(this,($.__proto__||Object.getPrototypeOf($)).call(this,Y,Q));return G.quill.root.addEventListener("paste",G.onPaste.bind(G)),G.container=G.quill.addContainer("ql-clipboard"),G.container.setAttribute("contenteditable",!0),G.container.setAttribute("tabindex",-1),G.matchers=[],D.concat(G.options.matchers).forEach(function(te){var ne=m(te,2),re=ne[0],he=ne[1];!Q.matchVisual&&he===oe||G.addMatcher(re,he)}),G}return b($,[{key:"addMatcher",value:function(Q,G){this.matchers.push([Q,G])}},{key:"convert",value:function(Q){if(typeof Q=="string")return this.container.innerHTML=Q.replace(/\>\r?\n +\</g,"><"),this.convert();var G=this.quill.getFormat(this.quill.selection.savedRange.index);if(G[A.default.blotName]){var te=this.container.innerText;return this.container.innerHTML="",new o.default().insert(te,N({},A.default.blotName,G[A.default.blotName]))}var ne=this.prepareMatching(),re=m(ne,2),he=re[0],pe=re[1],le=I(this.container,he,pe);return C(le,`
`)&&le.ops[le.ops.length-1].attributes==null&&(le=le.compose(new o.default().retain(le.length()-1).delete(1))),U.log("convert",this.container.innerHTML,le),this.container.innerHTML="",le}},{key:"dangerouslyPasteHTML",value:function(Q,G){var te=arguments.length>2&&arguments[2]!==void 0?arguments[2]:l.default.sources.API;if(typeof Q=="string")this.quill.setContents(this.convert(Q),G),this.quill.setSelection(0,l.default.sources.SILENT);else{var ne=this.convert(G);this.quill.updateContents(new o.default().retain(Q).concat(ne),te),this.quill.setSelection(Q+ne.length(),l.default.sources.SILENT)}}},{key:"onPaste",value:function(Q){var G=this;if(!(Q.defaultPrevented||!this.quill.isEnabled())){var te=this.quill.getSelection(),ne=new o.default().retain(te.index),re=this.quill.scrollingContainer.scrollTop;this.container.focus(),this.quill.selection.update(l.default.sources.SILENT),setTimeout(function(){ne=ne.concat(G.convert()).delete(te.length),G.quill.updateContents(ne,l.default.sources.USER),G.quill.setSelection(ne.length()-te.length,l.default.sources.SILENT),G.quill.scrollingContainer.scrollTop=re,G.quill.focus()},1)}}},{key:"prepareMatching",value:function(){var Q=this,G=[],te=[];return this.matchers.forEach(function(ne){var re=m(ne,2),he=re[0],pe=re[1];switch(he){case Node.TEXT_NODE:te.push(pe);break;case Node.ELEMENT_NODE:G.push(pe);break;default:[].forEach.call(Q.container.querySelectorAll(he),function(le){le[W]=le[W]||[],le[W].push(pe)});break}}),[G,te]}}]),$}(f.default);q.DEFAULTS={matchers:[],matchVisual:!0};function j(V,$,Y){return(typeof $>"u"?"undefined":O($))==="object"?Object.keys($).reduce(function(Q,G){return j(Q,G,$[G])},V):V.reduce(function(Q,G){return G.attributes&&G.attributes[$]?Q.push(G):Q.insert(G.insert,(0,y.default)({},N({},$,Y),G.attributes))},new o.default)}function H(V){if(V.nodeType!==Node.ELEMENT_NODE)return{};var $="__ql-computed-style";return V[$]||(V[$]=window.getComputedStyle(V))}function C(V,$){for(var Y="",Q=V.ops.length-1;Q>=0&&Y.length<$.length;--Q){var G=V.ops[Q];if(typeof G.insert!="string")break;Y=G.insert+Y}return Y.slice(-1*$.length)===$}function P(V){if(V.childNodes.length===0)return!1;var $=H(V);return["block","list-item"].indexOf($.display)>-1}function I(V,$,Y){return V.nodeType===V.TEXT_NODE?Y.reduce(function(Q,G){return G(V,Q)},new o.default):V.nodeType===V.ELEMENT_NODE?[].reduce.call(V.childNodes||[],function(Q,G){var te=I(G,$,Y);return G.nodeType===V.ELEMENT_NODE&&(te=$.reduce(function(ne,re){return re(G,ne)},te),te=(G[W]||[]).reduce(function(ne,re){return re(G,ne)},te)),Q.concat(te)},new o.default):new o.default}function z(V,$,Y){return j(Y,V,!0)}function K(V,$){var Y=t.default.Attributor.Attribute.keys(V),Q=t.default.Attributor.Class.keys(V),G=t.default.Attributor.Style.keys(V),te={};return Y.concat(Q).concat(G).forEach(function(ne){var re=t.default.query(ne,t.default.Scope.ATTRIBUTE);re!=null&&(te[re.attrName]=re.value(V),te[re.attrName])||(re=M[ne],re!=null&&(re.attrName===ne||re.keyName===ne)&&(te[re.attrName]=re.value(V)||void 0),re=T[ne],re!=null&&(re.attrName===ne||re.keyName===ne)&&(re=T[ne],te[re.attrName]=re.value(V)||void 0))}),Object.keys(te).length>0&&($=j($,te)),$}function Z(V,$){var Y=t.default.query(V);if(Y==null)return $;if(Y.prototype instanceof t.default.Embed){var Q={},G=Y.value(V);G!=null&&(Q[Y.blotName]=G,$=new o.default().insert(Q,Y.formats(V)))}else typeof Y.formats=="function"&&($=j($,Y.blotName,Y.formats(V)));return $}function X(V,$){return C($,`
`)||$.insert(`
`),$}function J(){return new o.default}function ee(V,$){var Y=t.default.query(V);if(Y==null||Y.blotName!=="list-item"||!C($,`
`))return $;for(var Q=-1,G=V.parentNode;!G.classList.contains("ql-clipboard");)(t.default.query(G)||{}).blotName==="list"&&(Q+=1),G=G.parentNode;return Q<=0?$:$.compose(new o.default().retain($.length()-1).retain(1,{indent:Q}))}function ie(V,$){return C($,`
`)||(P(V)||$.length()>0&&V.nextSibling&&P(V.nextSibling))&&$.insert(`
`),$}function oe(V,$){if(P(V)&&V.nextElementSibling!=null&&!C($,`

`)){var Y=V.offsetHeight+parseFloat(H(V).marginTop)+parseFloat(H(V).marginBottom);V.nextElementSibling.offsetTop>V.offsetTop+Y*1.5&&$.insert(`
`)}return $}function se(V,$){var Y={},Q=V.style||{};return Q.fontStyle&&H(V).fontStyle==="italic"&&(Y.italic=!0),Q.fontWeight&&(H(V).fontWeight.startsWith("bold")||parseInt(H(V).fontWeight)>=700)&&(Y.bold=!0),Object.keys(Y).length>0&&($=j($,Y)),parseFloat(Q.textIndent||0)>0&&($=new o.default().insert("	").concat($)),$}function ue(V,$){var Y=V.data;if(V.parentNode.tagName==="O:P")return $.insert(Y.trim());if(Y.trim().length===0&&V.parentNode.classList.contains("ql-clipboard"))return $;if(!H(V.parentNode).whiteSpace.startsWith("pre")){var Q=function(te,ne){return ne=ne.replace(/[^\u00a0]/g,""),ne.length<1&&te?" ":ne};Y=Y.replace(/\r\n/g," ").replace(/\n/g," "),Y=Y.replace(/\s\s+/g,Q.bind(Q,!0)),(V.previousSibling==null&&P(V.parentNode)||V.previousSibling!=null&&P(V.previousSibling))&&(Y=Y.replace(/^\s+/,Q.bind(Q,!1))),(V.nextSibling==null&&P(V.parentNode)||V.nextSibling!=null&&P(V.nextSibling))&&(Y=Y.replace(/\s+$/,Q.bind(Q,!1)))}return $.insert(Y)}v.default=q,v.matchAttributor=K,v.matchBlot=Z,v.matchNewline=ie,v.matchSpacing=oe,v.matchText=ue},function(g,v,c){Object.defineProperty(v,"__esModule",{value:!0});var O=function(){function u(l,a){for(var r=0;r<a.length;r++){var i=a[r];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(l,i.key,i)}}return function(l,a,r){return a&&u(l.prototype,a),r&&u(l,r),l}}(),m=function u(l,a,r){l===null&&(l=Function.prototype);var i=Object.getOwnPropertyDescriptor(l,a);if(i===void 0){var f=Object.getPrototypeOf(l);return f===null?void 0:u(f,a,r)}else{if("value"in i)return i.value;var n=i.get;return n===void 0?void 0:n.call(r)}},b=c(6),p=y(b);function y(u){return u&&u.__esModule?u:{default:u}}function s(u,l){if(!(u instanceof l))throw new TypeError("Cannot call a class as a function")}function o(u,l){if(!u)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return l&&(typeof l=="object"||typeof l=="function")?l:u}function e(u,l){if(typeof l!="function"&&l!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof l);u.prototype=Object.create(l&&l.prototype,{constructor:{value:u,enumerable:!1,writable:!0,configurable:!0}}),l&&(Object.setPrototypeOf?Object.setPrototypeOf(u,l):u.__proto__=l)}var t=function(u){e(l,u);function l(){return s(this,l),o(this,(l.__proto__||Object.getPrototypeOf(l)).apply(this,arguments))}return O(l,[{key:"optimize",value:function(r){m(l.prototype.__proto__||Object.getPrototypeOf(l.prototype),"optimize",this).call(this,r),this.domNode.tagName!==this.statics.tagName[0]&&this.replaceWith(this.statics.blotName)}}],[{key:"create",value:function(){return m(l.__proto__||Object.getPrototypeOf(l),"create",this).call(this)}},{key:"formats",value:function(){return!0}}]),l}(p.default);t.blotName="bold",t.tagName=["STRONG","B"],v.default=t},function(g,v,c){Object.defineProperty(v,"__esModule",{value:!0}),v.addControls=v.default=void 0;var O=function(){function d(_,N){var x=[],R=!0,F=!1,U=void 0;try{for(var W=_[Symbol.iterator](),D;!(R=(D=W.next()).done)&&(x.push(D.value),!(N&&x.length===N));R=!0);}catch(M){F=!0,U=M}finally{try{!R&&W.return&&W.return()}finally{if(F)throw U}}return x}return function(_,N){if(Array.isArray(_))return _;if(Symbol.iterator in Object(_))return d(_,N);throw new TypeError("Invalid attempt to destructure non-iterable instance")}}(),m=function(){function d(_,N){for(var x=0;x<N.length;x++){var R=N[x];R.enumerable=R.enumerable||!1,R.configurable=!0,"value"in R&&(R.writable=!0),Object.defineProperty(_,R.key,R)}}return function(_,N,x){return N&&d(_.prototype,N),x&&d(_,x),_}}(),b=c(2),p=r(b),y=c(0),s=r(y),o=c(5),e=r(o),t=c(10),u=r(t),l=c(9),a=r(l);function r(d){return d&&d.__esModule?d:{default:d}}function i(d,_,N){return _ in d?Object.defineProperty(d,_,{value:N,enumerable:!0,configurable:!0,writable:!0}):d[_]=N,d}function f(d,_){if(!(d instanceof _))throw new TypeError("Cannot call a class as a function")}function n(d,_){if(!d)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return _&&(typeof _=="object"||typeof _=="function")?_:d}function h(d,_){if(typeof _!="function"&&_!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof _);d.prototype=Object.create(_&&_.prototype,{constructor:{value:d,enumerable:!1,writable:!0,configurable:!0}}),_&&(Object.setPrototypeOf?Object.setPrototypeOf(d,_):d.__proto__=_)}var w=(0,u.default)("quill:toolbar"),A=function(d){h(_,d);function _(N,x){f(this,_);var R=n(this,(_.__proto__||Object.getPrototypeOf(_)).call(this,N,x));if(Array.isArray(R.options.container)){var F=document.createElement("div");S(F,R.options.container),N.container.parentNode.insertBefore(F,N.container),R.container=F}else typeof R.options.container=="string"?R.container=document.querySelector(R.options.container):R.container=R.options.container;if(!(R.container instanceof HTMLElement)){var U;return U=w.error("Container required for toolbar",R.options),n(R,U)}return R.container.classList.add("ql-toolbar"),R.controls=[],R.handlers={},Object.keys(R.options.handlers).forEach(function(W){R.addHandler(W,R.options.handlers[W])}),[].forEach.call(R.container.querySelectorAll("button, select"),function(W){R.attach(W)}),R.quill.on(e.default.events.EDITOR_CHANGE,function(W,D){W===e.default.events.SELECTION_CHANGE&&R.update(D)}),R.quill.on(e.default.events.SCROLL_OPTIMIZE,function(){var W=R.quill.selection.getRange(),D=O(W,1),M=D[0];R.update(M)}),R}return m(_,[{key:"addHandler",value:function(x,R){this.handlers[x]=R}},{key:"attach",value:function(x){var R=this,F=[].find.call(x.classList,function(W){return W.indexOf("ql-")===0});if(F){if(F=F.slice(3),x.tagName==="BUTTON"&&x.setAttribute("type","button"),this.handlers[F]==null){if(this.quill.scroll.whitelist!=null&&this.quill.scroll.whitelist[F]==null){w.warn("ignoring attaching to disabled format",F,x);return}if(s.default.query(F)==null){w.warn("ignoring attaching to nonexistent format",F,x);return}}var U=x.tagName==="SELECT"?"change":"click";x.addEventListener(U,function(W){var D=void 0;if(x.tagName==="SELECT"){if(x.selectedIndex<0)return;var M=x.options[x.selectedIndex];M.hasAttribute("selected")?D=!1:D=M.value||!1}else x.classList.contains("ql-active")?D=!1:D=x.value||!x.hasAttribute("value"),W.preventDefault();R.quill.focus();var T=R.quill.selection.getRange(),q=O(T,1),j=q[0];if(R.handlers[F]!=null)R.handlers[F].call(R,D);else if(s.default.query(F).prototype instanceof s.default.Embed){if(D=prompt("Enter "+F),!D)return;R.quill.updateContents(new p.default().retain(j.index).delete(j.length).insert(i({},F,D)),e.default.sources.USER)}else R.quill.format(F,D,e.default.sources.USER);R.update(j)}),this.controls.push([F,x])}}},{key:"update",value:function(x){var R=x==null?{}:this.quill.getFormat(x);this.controls.forEach(function(F){var U=O(F,2),W=U[0],D=U[1];if(D.tagName==="SELECT"){var M=void 0;if(x==null)M=null;else if(R[W]==null)M=D.querySelector("option[selected]");else if(!Array.isArray(R[W])){var T=R[W];typeof T=="string"&&(T=T.replace(/\"/g,'\\"')),M=D.querySelector('option[value="'+T+'"]')}M==null?(D.value="",D.selectedIndex=-1):M.selected=!0}else if(x==null)D.classList.remove("ql-active");else if(D.hasAttribute("value")){var q=R[W]===D.getAttribute("value")||R[W]!=null&&R[W].toString()===D.getAttribute("value")||R[W]==null&&!D.getAttribute("value");D.classList.toggle("ql-active",q)}else D.classList.toggle("ql-active",R[W]!=null)})}}]),_}(a.default);A.DEFAULTS={};function k(d,_,N){var x=document.createElement("button");x.setAttribute("type","button"),x.classList.add("ql-"+_),N!=null&&(x.value=N),d.appendChild(x)}function S(d,_){Array.isArray(_[0])||(_=[_]),_.forEach(function(N){var x=document.createElement("span");x.classList.add("ql-formats"),N.forEach(function(R){if(typeof R=="string")k(x,R);else{var F=Object.keys(R)[0],U=R[F];Array.isArray(U)?E(x,F,U):k(x,F,U)}}),d.appendChild(x)})}function E(d,_,N){var x=document.createElement("select");x.classList.add("ql-"+_),N.forEach(function(R){var F=document.createElement("option");R!==!1?F.setAttribute("value",R):F.setAttribute("selected","selected"),x.appendChild(F)}),d.appendChild(x)}A.DEFAULTS={container:null,handlers:{clean:function(){var _=this,N=this.quill.getSelection();if(N!=null)if(N.length==0){var x=this.quill.getFormat();Object.keys(x).forEach(function(R){s.default.query(R,s.default.Scope.INLINE)!=null&&_.quill.format(R,!1)})}else this.quill.removeFormat(N,e.default.sources.USER)},direction:function(_){var N=this.quill.getFormat().align;_==="rtl"&&N==null?this.quill.format("align","right",e.default.sources.USER):!_&&N==="right"&&this.quill.format("align",!1,e.default.sources.USER),this.quill.format("direction",_,e.default.sources.USER)},indent:function(_){var N=this.quill.getSelection(),x=this.quill.getFormat(N),R=parseInt(x.indent||0);if(_==="+1"||_==="-1"){var F=_==="+1"?1:-1;x.direction==="rtl"&&(F*=-1),this.quill.format("indent",R+F,e.default.sources.USER)}},link:function(_){_===!0&&(_=prompt("Enter link URL:")),this.quill.format("link",_,e.default.sources.USER)},list:function(_){var N=this.quill.getSelection(),x=this.quill.getFormat(N);_==="check"?x.list==="checked"||x.list==="unchecked"?this.quill.format("list",!1,e.default.sources.USER):this.quill.format("list","unchecked",e.default.sources.USER):this.quill.format("list",_,e.default.sources.USER)}}},v.default=A,v.addControls=S},function(g,v){g.exports='<svg viewbox="0 0 18 18"> <polyline class="ql-even ql-stroke" points="5 7 3 9 5 11"></polyline> <polyline class="ql-even ql-stroke" points="13 7 15 9 13 11"></polyline> <line class=ql-stroke x1=10 x2=8 y1=5 y2=13></line> </svg>'},function(g,v,c){Object.defineProperty(v,"__esModule",{value:!0});var O=function(){function u(l,a){for(var r=0;r<a.length;r++){var i=a[r];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(l,i.key,i)}}return function(l,a,r){return a&&u(l.prototype,a),r&&u(l,r),l}}(),m=function u(l,a,r){l===null&&(l=Function.prototype);var i=Object.getOwnPropertyDescriptor(l,a);if(i===void 0){var f=Object.getPrototypeOf(l);return f===null?void 0:u(f,a,r)}else{if("value"in i)return i.value;var n=i.get;return n===void 0?void 0:n.call(r)}},b=c(28),p=y(b);function y(u){return u&&u.__esModule?u:{default:u}}function s(u,l){if(!(u instanceof l))throw new TypeError("Cannot call a class as a function")}function o(u,l){if(!u)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return l&&(typeof l=="object"||typeof l=="function")?l:u}function e(u,l){if(typeof l!="function"&&l!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof l);u.prototype=Object.create(l&&l.prototype,{constructor:{value:u,enumerable:!1,writable:!0,configurable:!0}}),l&&(Object.setPrototypeOf?Object.setPrototypeOf(u,l):u.__proto__=l)}var t=function(u){e(l,u);function l(a,r){s(this,l);var i=o(this,(l.__proto__||Object.getPrototypeOf(l)).call(this,a));return i.label.innerHTML=r,i.container.classList.add("ql-color-picker"),[].slice.call(i.container.querySelectorAll(".ql-picker-item"),0,7).forEach(function(f){f.classList.add("ql-primary")}),i}return O(l,[{key:"buildItem",value:function(r){var i=m(l.prototype.__proto__||Object.getPrototypeOf(l.prototype),"buildItem",this).call(this,r);return i.style.backgroundColor=r.getAttribute("value")||"",i}},{key:"selectItem",value:function(r,i){m(l.prototype.__proto__||Object.getPrototypeOf(l.prototype),"selectItem",this).call(this,r,i);var f=this.label.querySelector(".ql-color-label"),n=r&&r.getAttribute("data-value")||"";f&&(f.tagName==="line"?f.style.stroke=n:f.style.fill=n)}}]),l}(p.default);v.default=t},function(g,v,c){Object.defineProperty(v,"__esModule",{value:!0});var O=function(){function u(l,a){for(var r=0;r<a.length;r++){var i=a[r];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(l,i.key,i)}}return function(l,a,r){return a&&u(l.prototype,a),r&&u(l,r),l}}(),m=function u(l,a,r){l===null&&(l=Function.prototype);var i=Object.getOwnPropertyDescriptor(l,a);if(i===void 0){var f=Object.getPrototypeOf(l);return f===null?void 0:u(f,a,r)}else{if("value"in i)return i.value;var n=i.get;return n===void 0?void 0:n.call(r)}},b=c(28),p=y(b);function y(u){return u&&u.__esModule?u:{default:u}}function s(u,l){if(!(u instanceof l))throw new TypeError("Cannot call a class as a function")}function o(u,l){if(!u)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return l&&(typeof l=="object"||typeof l=="function")?l:u}function e(u,l){if(typeof l!="function"&&l!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof l);u.prototype=Object.create(l&&l.prototype,{constructor:{value:u,enumerable:!1,writable:!0,configurable:!0}}),l&&(Object.setPrototypeOf?Object.setPrototypeOf(u,l):u.__proto__=l)}var t=function(u){e(l,u);function l(a,r){s(this,l);var i=o(this,(l.__proto__||Object.getPrototypeOf(l)).call(this,a));return i.container.classList.add("ql-icon-picker"),[].forEach.call(i.container.querySelectorAll(".ql-picker-item"),function(f){f.innerHTML=r[f.getAttribute("data-value")||""]}),i.defaultItem=i.container.querySelector(".ql-selected"),i.selectItem(i.defaultItem),i}return O(l,[{key:"selectItem",value:function(r,i){m(l.prototype.__proto__||Object.getPrototypeOf(l.prototype),"selectItem",this).call(this,r,i),r=r||this.defaultItem,this.label.innerHTML=r.innerHTML}}]),l}(p.default);v.default=t},function(g,v,c){Object.defineProperty(v,"__esModule",{value:!0});var O=function(){function p(y,s){for(var o=0;o<s.length;o++){var e=s[o];e.enumerable=e.enumerable||!1,e.configurable=!0,"value"in e&&(e.writable=!0),Object.defineProperty(y,e.key,e)}}return function(y,s,o){return s&&p(y.prototype,s),o&&p(y,o),y}}();function m(p,y){if(!(p instanceof y))throw new TypeError("Cannot call a class as a function")}var b=function(){function p(y,s){var o=this;m(this,p),this.quill=y,this.boundsContainer=s||document.body,this.root=y.addContainer("ql-tooltip"),this.root.innerHTML=this.constructor.TEMPLATE,this.quill.root===this.quill.scrollingContainer&&this.quill.root.addEventListener("scroll",function(){o.root.style.marginTop=-1*o.quill.root.scrollTop+"px"}),this.hide()}return O(p,[{key:"hide",value:function(){this.root.classList.add("ql-hidden")}},{key:"position",value:function(s){var o=s.left+s.width/2-this.root.offsetWidth/2,e=s.bottom+this.quill.root.scrollTop;this.root.style.left=o+"px",this.root.style.top=e+"px",this.root.classList.remove("ql-flip");var t=this.boundsContainer.getBoundingClientRect(),u=this.root.getBoundingClientRect(),l=0;if(u.right>t.right&&(l=t.right-u.right,this.root.style.left=o+l+"px"),u.left<t.left&&(l=t.left-u.left,this.root.style.left=o+l+"px"),u.bottom>t.bottom){var a=u.bottom-u.top,r=s.bottom-s.top+a;this.root.style.top=e-r+"px",this.root.classList.add("ql-flip")}return l}},{key:"show",value:function(){this.root.classList.remove("ql-editing"),this.root.classList.remove("ql-hidden")}}]),p}();v.default=b},function(g,v,c){Object.defineProperty(v,"__esModule",{value:!0});var O=function(){function E(d,_){var N=[],x=!0,R=!1,F=void 0;try{for(var U=d[Symbol.iterator](),W;!(x=(W=U.next()).done)&&(N.push(W.value),!(_&&N.length===_));x=!0);}catch(D){R=!0,F=D}finally{try{!x&&U.return&&U.return()}finally{if(R)throw F}}return N}return function(d,_){if(Array.isArray(d))return d;if(Symbol.iterator in Object(d))return E(d,_);throw new TypeError("Invalid attempt to destructure non-iterable instance")}}(),m=function E(d,_,N){d===null&&(d=Function.prototype);var x=Object.getOwnPropertyDescriptor(d,_);if(x===void 0){var R=Object.getPrototypeOf(d);return R===null?void 0:E(R,_,N)}else{if("value"in x)return x.value;var F=x.get;return F===void 0?void 0:F.call(N)}},b=function(){function E(d,_){for(var N=0;N<_.length;N++){var x=_[N];x.enumerable=x.enumerable||!1,x.configurable=!0,"value"in x&&(x.writable=!0),Object.defineProperty(d,x.key,x)}}return function(d,_,N){return _&&E(d.prototype,_),N&&E(d,N),d}}(),p=c(3),y=f(p),s=c(8),o=f(s),e=c(43),t=f(e),u=c(27),l=f(u),a=c(15),r=c(41),i=f(r);function f(E){return E&&E.__esModule?E:{default:E}}function n(E,d){if(!(E instanceof d))throw new TypeError("Cannot call a class as a function")}function h(E,d){if(!E)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return d&&(typeof d=="object"||typeof d=="function")?d:E}function w(E,d){if(typeof d!="function"&&d!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof d);E.prototype=Object.create(d&&d.prototype,{constructor:{value:E,enumerable:!1,writable:!0,configurable:!0}}),d&&(Object.setPrototypeOf?Object.setPrototypeOf(E,d):E.__proto__=d)}var A=[[{header:["1","2","3",!1]}],["bold","italic","underline","link"],[{list:"ordered"},{list:"bullet"}],["clean"]],k=function(E){w(d,E);function d(_,N){n(this,d),N.modules.toolbar!=null&&N.modules.toolbar.container==null&&(N.modules.toolbar.container=A);var x=h(this,(d.__proto__||Object.getPrototypeOf(d)).call(this,_,N));return x.quill.container.classList.add("ql-snow"),x}return b(d,[{key:"extendToolbar",value:function(N){N.container.classList.add("ql-snow"),this.buildButtons([].slice.call(N.container.querySelectorAll("button")),i.default),this.buildPickers([].slice.call(N.container.querySelectorAll("select")),i.default),this.tooltip=new S(this.quill,this.options.bounds),N.container.querySelector(".ql-link")&&this.quill.keyboard.addBinding({key:"K",shortKey:!0},function(x,R){N.handlers.link.call(N,!R.format.link)})}}]),d}(t.default);k.DEFAULTS=(0,y.default)(!0,{},t.default.DEFAULTS,{modules:{toolbar:{handlers:{link:function(d){if(d){var _=this.quill.getSelection();if(_==null||_.length==0)return;var N=this.quill.getText(_);/^\S+@\S+\.\S+$/.test(N)&&N.indexOf("mailto:")!==0&&(N="mailto:"+N);var x=this.quill.theme.tooltip;x.edit("link",N)}else this.quill.format("link",!1)}}}}});var S=function(E){w(d,E);function d(_,N){n(this,d);var x=h(this,(d.__proto__||Object.getPrototypeOf(d)).call(this,_,N));return x.preview=x.root.querySelector("a.ql-preview"),x}return b(d,[{key:"listen",value:function(){var N=this;m(d.prototype.__proto__||Object.getPrototypeOf(d.prototype),"listen",this).call(this),this.root.querySelector("a.ql-action").addEventListener("click",function(x){N.root.classList.contains("ql-editing")?N.save():N.edit("link",N.preview.textContent),x.preventDefault()}),this.root.querySelector("a.ql-remove").addEventListener("click",function(x){if(N.linkRange!=null){var R=N.linkRange;N.restoreFocus(),N.quill.formatText(R,"link",!1,o.default.sources.USER),delete N.linkRange}x.preventDefault(),N.hide()}),this.quill.on(o.default.events.SELECTION_CHANGE,function(x,R,F){if(x!=null){if(x.length===0&&F===o.default.sources.USER){var U=N.quill.scroll.descendant(l.default,x.index),W=O(U,2),D=W[0],M=W[1];if(D!=null){N.linkRange=new a.Range(x.index-M,D.length());var T=l.default.formats(D.domNode);N.preview.textContent=T,N.preview.setAttribute("href",T),N.show(),N.position(N.quill.getBounds(N.linkRange));return}}else delete N.linkRange;N.hide()}})}},{key:"show",value:function(){m(d.prototype.__proto__||Object.getPrototypeOf(d.prototype),"show",this).call(this),this.root.removeAttribute("data-mode")}}]),d}(e.BaseTooltip);S.TEMPLATE=['<a class="ql-preview" rel="noopener noreferrer" target="_blank" href="about:blank"></a>','<input type="text" data-formula="e=mc^2" data-link="https://quilljs.com" data-video="Embed URL">','<a class="ql-action"></a>','<a class="ql-remove"></a>'].join(""),v.default=k},function(g,v,c){Object.defineProperty(v,"__esModule",{value:!0});var O=c(29),m=G(O),b=c(36),p=c(38),y=c(64),s=c(65),o=G(s),e=c(66),t=G(e),u=c(67),l=G(u),a=c(37),r=c(26),i=c(39),f=c(40),n=c(56),h=G(n),w=c(68),A=G(w),k=c(27),S=G(k),E=c(69),d=G(E),_=c(70),N=G(_),x=c(71),R=G(x),F=c(72),U=G(F),W=c(73),D=G(W),M=c(13),T=G(M),q=c(74),j=G(q),H=c(75),C=G(H),P=c(57),I=G(P),z=c(41),K=G(z),Z=c(28),X=G(Z),J=c(59),ee=G(J),ie=c(60),oe=G(ie),se=c(61),ue=G(se),V=c(108),$=G(V),Y=c(62),Q=G(Y);function G(te){return te&&te.__esModule?te:{default:te}}m.default.register({"attributors/attribute/direction":p.DirectionAttribute,"attributors/class/align":b.AlignClass,"attributors/class/background":a.BackgroundClass,"attributors/class/color":r.ColorClass,"attributors/class/direction":p.DirectionClass,"attributors/class/font":i.FontClass,"attributors/class/size":f.SizeClass,"attributors/style/align":b.AlignStyle,"attributors/style/background":a.BackgroundStyle,"attributors/style/color":r.ColorStyle,"attributors/style/direction":p.DirectionStyle,"attributors/style/font":i.FontStyle,"attributors/style/size":f.SizeStyle},!0),m.default.register({"formats/align":b.AlignClass,"formats/direction":p.DirectionClass,"formats/indent":y.IndentClass,"formats/background":a.BackgroundStyle,"formats/color":r.ColorStyle,"formats/font":i.FontClass,"formats/size":f.SizeClass,"formats/blockquote":o.default,"formats/code-block":T.default,"formats/header":t.default,"formats/list":l.default,"formats/bold":h.default,"formats/code":M.Code,"formats/italic":A.default,"formats/link":S.default,"formats/script":d.default,"formats/strike":N.default,"formats/underline":R.default,"formats/image":U.default,"formats/video":D.default,"formats/list/item":u.ListItem,"modules/formula":j.default,"modules/syntax":C.default,"modules/toolbar":I.default,"themes/bubble":$.default,"themes/snow":Q.default,"ui/icons":K.default,"ui/picker":X.default,"ui/icon-picker":oe.default,"ui/color-picker":ee.default,"ui/tooltip":ue.default},!0),v.default=m.default},function(g,v,c){Object.defineProperty(v,"__esModule",{value:!0}),v.IndentClass=void 0;var O=function(){function l(a,r){for(var i=0;i<r.length;i++){var f=r[i];f.enumerable=f.enumerable||!1,f.configurable=!0,"value"in f&&(f.writable=!0),Object.defineProperty(a,f.key,f)}}return function(a,r,i){return r&&l(a.prototype,r),i&&l(a,i),a}}(),m=function l(a,r,i){a===null&&(a=Function.prototype);var f=Object.getOwnPropertyDescriptor(a,r);if(f===void 0){var n=Object.getPrototypeOf(a);return n===null?void 0:l(n,r,i)}else{if("value"in f)return f.value;var h=f.get;return h===void 0?void 0:h.call(i)}},b=c(0),p=y(b);function y(l){return l&&l.__esModule?l:{default:l}}function s(l,a){if(!(l instanceof a))throw new TypeError("Cannot call a class as a function")}function o(l,a){if(!l)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return a&&(typeof a=="object"||typeof a=="function")?a:l}function e(l,a){if(typeof a!="function"&&a!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof a);l.prototype=Object.create(a&&a.prototype,{constructor:{value:l,enumerable:!1,writable:!0,configurable:!0}}),a&&(Object.setPrototypeOf?Object.setPrototypeOf(l,a):l.__proto__=a)}var t=function(l){e(a,l);function a(){return s(this,a),o(this,(a.__proto__||Object.getPrototypeOf(a)).apply(this,arguments))}return O(a,[{key:"add",value:function(i,f){if(f==="+1"||f==="-1"){var n=this.value(i)||0;f=f==="+1"?n+1:n-1}return f===0?(this.remove(i),!0):m(a.prototype.__proto__||Object.getPrototypeOf(a.prototype),"add",this).call(this,i,f)}},{key:"canAdd",value:function(i,f){return m(a.prototype.__proto__||Object.getPrototypeOf(a.prototype),"canAdd",this).call(this,i,f)||m(a.prototype.__proto__||Object.getPrototypeOf(a.prototype),"canAdd",this).call(this,i,parseInt(f))}},{key:"value",value:function(i){return parseInt(m(a.prototype.__proto__||Object.getPrototypeOf(a.prototype),"value",this).call(this,i))||void 0}}]),a}(p.default.Attributor.Class),u=new t("indent","ql-indent",{scope:p.default.Scope.BLOCK,whitelist:[1,2,3,4,5,6,7,8]});v.IndentClass=u},function(g,v,c){Object.defineProperty(v,"__esModule",{value:!0});var O=c(4),m=b(O);function b(e){return e&&e.__esModule?e:{default:e}}function p(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function y(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t&&(typeof t=="object"||typeof t=="function")?t:e}function s(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}var o=function(e){s(t,e);function t(){return p(this,t),y(this,(t.__proto__||Object.getPrototypeOf(t)).apply(this,arguments))}return t}(m.default);o.blotName="blockquote",o.tagName="blockquote",v.default=o},function(g,v,c){Object.defineProperty(v,"__esModule",{value:!0});var O=function(){function t(u,l){for(var a=0;a<l.length;a++){var r=l[a];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(u,r.key,r)}}return function(u,l,a){return l&&t(u.prototype,l),a&&t(u,a),u}}(),m=c(4),b=p(m);function p(t){return t&&t.__esModule?t:{default:t}}function y(t,u){if(!(t instanceof u))throw new TypeError("Cannot call a class as a function")}function s(t,u){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return u&&(typeof u=="object"||typeof u=="function")?u:t}function o(t,u){if(typeof u!="function"&&u!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof u);t.prototype=Object.create(u&&u.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),u&&(Object.setPrototypeOf?Object.setPrototypeOf(t,u):t.__proto__=u)}var e=function(t){o(u,t);function u(){return y(this,u),s(this,(u.__proto__||Object.getPrototypeOf(u)).apply(this,arguments))}return O(u,null,[{key:"formats",value:function(a){return this.tagName.indexOf(a.tagName)+1}}]),u}(b.default);e.blotName="header",e.tagName=["H1","H2","H3","H4","H5","H6"],v.default=e},function(g,v,c){Object.defineProperty(v,"__esModule",{value:!0}),v.default=v.ListItem=void 0;var O=function(){function n(h,w){for(var A=0;A<w.length;A++){var k=w[A];k.enumerable=k.enumerable||!1,k.configurable=!0,"value"in k&&(k.writable=!0),Object.defineProperty(h,k.key,k)}}return function(h,w,A){return w&&n(h.prototype,w),A&&n(h,A),h}}(),m=function n(h,w,A){h===null&&(h=Function.prototype);var k=Object.getOwnPropertyDescriptor(h,w);if(k===void 0){var S=Object.getPrototypeOf(h);return S===null?void 0:n(S,w,A)}else{if("value"in k)return k.value;var E=k.get;return E===void 0?void 0:E.call(A)}},b=c(0),p=t(b),y=c(4),s=t(y),o=c(25),e=t(o);function t(n){return n&&n.__esModule?n:{default:n}}function u(n,h,w){return h in n?Object.defineProperty(n,h,{value:w,enumerable:!0,configurable:!0,writable:!0}):n[h]=w,n}function l(n,h){if(!(n instanceof h))throw new TypeError("Cannot call a class as a function")}function a(n,h){if(!n)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return h&&(typeof h=="object"||typeof h=="function")?h:n}function r(n,h){if(typeof h!="function"&&h!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof h);n.prototype=Object.create(h&&h.prototype,{constructor:{value:n,enumerable:!1,writable:!0,configurable:!0}}),h&&(Object.setPrototypeOf?Object.setPrototypeOf(n,h):n.__proto__=h)}var i=function(n){r(h,n);function h(){return l(this,h),a(this,(h.__proto__||Object.getPrototypeOf(h)).apply(this,arguments))}return O(h,[{key:"format",value:function(A,k){A===f.blotName&&!k?this.replaceWith(p.default.create(this.statics.scope)):m(h.prototype.__proto__||Object.getPrototypeOf(h.prototype),"format",this).call(this,A,k)}},{key:"remove",value:function(){this.prev==null&&this.next==null?this.parent.remove():m(h.prototype.__proto__||Object.getPrototypeOf(h.prototype),"remove",this).call(this)}},{key:"replaceWith",value:function(A,k){return this.parent.isolate(this.offset(this.parent),this.length()),A===this.parent.statics.blotName?(this.parent.replaceWith(A,k),this):(this.parent.unwrap(),m(h.prototype.__proto__||Object.getPrototypeOf(h.prototype),"replaceWith",this).call(this,A,k))}}],[{key:"formats",value:function(A){return A.tagName===this.tagName?void 0:m(h.__proto__||Object.getPrototypeOf(h),"formats",this).call(this,A)}}]),h}(s.default);i.blotName="list-item",i.tagName="LI";var f=function(n){r(h,n),O(h,null,[{key:"create",value:function(A){var k=A==="ordered"?"OL":"UL",S=m(h.__proto__||Object.getPrototypeOf(h),"create",this).call(this,k);return(A==="checked"||A==="unchecked")&&S.setAttribute("data-checked",A==="checked"),S}},{key:"formats",value:function(A){if(A.tagName==="OL")return"ordered";if(A.tagName==="UL")return A.hasAttribute("data-checked")?A.getAttribute("data-checked")==="true"?"checked":"unchecked":"bullet"}}]);function h(w){l(this,h);var A=a(this,(h.__proto__||Object.getPrototypeOf(h)).call(this,w)),k=function(E){if(E.target.parentNode===w){var d=A.statics.formats(w),_=p.default.find(E.target);d==="checked"?_.format("list","unchecked"):d==="unchecked"&&_.format("list","checked")}};return w.addEventListener("touchstart",k),w.addEventListener("mousedown",k),A}return O(h,[{key:"format",value:function(A,k){this.children.length>0&&this.children.tail.format(A,k)}},{key:"formats",value:function(){return u({},this.statics.blotName,this.statics.formats(this.domNode))}},{key:"insertBefore",value:function(A,k){if(A instanceof i)m(h.prototype.__proto__||Object.getPrototypeOf(h.prototype),"insertBefore",this).call(this,A,k);else{var S=k==null?this.length():k.offset(this),E=this.split(S);E.parent.insertBefore(A,E)}}},{key:"optimize",value:function(A){m(h.prototype.__proto__||Object.getPrototypeOf(h.prototype),"optimize",this).call(this,A);var k=this.next;k!=null&&k.prev===this&&k.statics.blotName===this.statics.blotName&&k.domNode.tagName===this.domNode.tagName&&k.domNode.getAttribute("data-checked")===this.domNode.getAttribute("data-checked")&&(k.moveChildren(this),k.remove())}},{key:"replace",value:function(A){if(A.statics.blotName!==this.statics.blotName){var k=p.default.create(this.statics.defaultChild);A.moveChildren(k),this.appendChild(k)}m(h.prototype.__proto__||Object.getPrototypeOf(h.prototype),"replace",this).call(this,A)}}]),h}(e.default);f.blotName="list",f.scope=p.default.Scope.BLOCK_BLOT,f.tagName=["OL","UL"],f.defaultChild="list-item",f.allowedChildren=[i],v.ListItem=i,v.default=f},function(g,v,c){Object.defineProperty(v,"__esModule",{value:!0});var O=c(56),m=b(O);function b(e){return e&&e.__esModule?e:{default:e}}function p(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function y(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t&&(typeof t=="object"||typeof t=="function")?t:e}function s(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}var o=function(e){s(t,e);function t(){return p(this,t),y(this,(t.__proto__||Object.getPrototypeOf(t)).apply(this,arguments))}return t}(m.default);o.blotName="italic",o.tagName=["EM","I"],v.default=o},function(g,v,c){Object.defineProperty(v,"__esModule",{value:!0});var O=function(){function u(l,a){for(var r=0;r<a.length;r++){var i=a[r];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(l,i.key,i)}}return function(l,a,r){return a&&u(l.prototype,a),r&&u(l,r),l}}(),m=function u(l,a,r){l===null&&(l=Function.prototype);var i=Object.getOwnPropertyDescriptor(l,a);if(i===void 0){var f=Object.getPrototypeOf(l);return f===null?void 0:u(f,a,r)}else{if("value"in i)return i.value;var n=i.get;return n===void 0?void 0:n.call(r)}},b=c(6),p=y(b);function y(u){return u&&u.__esModule?u:{default:u}}function s(u,l){if(!(u instanceof l))throw new TypeError("Cannot call a class as a function")}function o(u,l){if(!u)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return l&&(typeof l=="object"||typeof l=="function")?l:u}function e(u,l){if(typeof l!="function"&&l!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof l);u.prototype=Object.create(l&&l.prototype,{constructor:{value:u,enumerable:!1,writable:!0,configurable:!0}}),l&&(Object.setPrototypeOf?Object.setPrototypeOf(u,l):u.__proto__=l)}var t=function(u){e(l,u);function l(){return s(this,l),o(this,(l.__proto__||Object.getPrototypeOf(l)).apply(this,arguments))}return O(l,null,[{key:"create",value:function(r){return r==="super"?document.createElement("sup"):r==="sub"?document.createElement("sub"):m(l.__proto__||Object.getPrototypeOf(l),"create",this).call(this,r)}},{key:"formats",value:function(r){if(r.tagName==="SUB")return"sub";if(r.tagName==="SUP")return"super"}}]),l}(p.default);t.blotName="script",t.tagName=["SUB","SUP"],v.default=t},function(g,v,c){Object.defineProperty(v,"__esModule",{value:!0});var O=c(6),m=b(O);function b(e){return e&&e.__esModule?e:{default:e}}function p(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function y(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t&&(typeof t=="object"||typeof t=="function")?t:e}function s(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}var o=function(e){s(t,e);function t(){return p(this,t),y(this,(t.__proto__||Object.getPrototypeOf(t)).apply(this,arguments))}return t}(m.default);o.blotName="strike",o.tagName="S",v.default=o},function(g,v,c){Object.defineProperty(v,"__esModule",{value:!0});var O=c(6),m=b(O);function b(e){return e&&e.__esModule?e:{default:e}}function p(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function y(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t&&(typeof t=="object"||typeof t=="function")?t:e}function s(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}var o=function(e){s(t,e);function t(){return p(this,t),y(this,(t.__proto__||Object.getPrototypeOf(t)).apply(this,arguments))}return t}(m.default);o.blotName="underline",o.tagName="U",v.default=o},function(g,v,c){Object.defineProperty(v,"__esModule",{value:!0});var O=function(){function a(r,i){for(var f=0;f<i.length;f++){var n=i[f];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(r,n.key,n)}}return function(r,i,f){return i&&a(r.prototype,i),f&&a(r,f),r}}(),m=function a(r,i,f){r===null&&(r=Function.prototype);var n=Object.getOwnPropertyDescriptor(r,i);if(n===void 0){var h=Object.getPrototypeOf(r);return h===null?void 0:a(h,i,f)}else{if("value"in n)return n.value;var w=n.get;return w===void 0?void 0:w.call(f)}},b=c(0),p=s(b),y=c(27);function s(a){return a&&a.__esModule?a:{default:a}}function o(a,r){if(!(a instanceof r))throw new TypeError("Cannot call a class as a function")}function e(a,r){if(!a)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return r&&(typeof r=="object"||typeof r=="function")?r:a}function t(a,r){if(typeof r!="function"&&r!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof r);a.prototype=Object.create(r&&r.prototype,{constructor:{value:a,enumerable:!1,writable:!0,configurable:!0}}),r&&(Object.setPrototypeOf?Object.setPrototypeOf(a,r):a.__proto__=r)}var u=["alt","height","width"],l=function(a){t(r,a);function r(){return o(this,r),e(this,(r.__proto__||Object.getPrototypeOf(r)).apply(this,arguments))}return O(r,[{key:"format",value:function(f,n){u.indexOf(f)>-1?n?this.domNode.setAttribute(f,n):this.domNode.removeAttribute(f):m(r.prototype.__proto__||Object.getPrototypeOf(r.prototype),"format",this).call(this,f,n)}}],[{key:"create",value:function(f){var n=m(r.__proto__||Object.getPrototypeOf(r),"create",this).call(this,f);return typeof f=="string"&&n.setAttribute("src",this.sanitize(f)),n}},{key:"formats",value:function(f){return u.reduce(function(n,h){return f.hasAttribute(h)&&(n[h]=f.getAttribute(h)),n},{})}},{key:"match",value:function(f){return/\.(jpe?g|gif|png)$/.test(f)||/^data:image\/.+;base64/.test(f)}},{key:"sanitize",value:function(f){return(0,y.sanitize)(f,["http","https","data"])?f:"//:0"}},{key:"value",value:function(f){return f.getAttribute("src")}}]),r}(p.default.Embed);l.blotName="image",l.tagName="IMG",v.default=l},function(g,v,c){Object.defineProperty(v,"__esModule",{value:!0});var O=function(){function a(r,i){for(var f=0;f<i.length;f++){var n=i[f];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(r,n.key,n)}}return function(r,i,f){return i&&a(r.prototype,i),f&&a(r,f),r}}(),m=function a(r,i,f){r===null&&(r=Function.prototype);var n=Object.getOwnPropertyDescriptor(r,i);if(n===void 0){var h=Object.getPrototypeOf(r);return h===null?void 0:a(h,i,f)}else{if("value"in n)return n.value;var w=n.get;return w===void 0?void 0:w.call(f)}},b=c(4),p=c(27),y=s(p);function s(a){return a&&a.__esModule?a:{default:a}}function o(a,r){if(!(a instanceof r))throw new TypeError("Cannot call a class as a function")}function e(a,r){if(!a)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return r&&(typeof r=="object"||typeof r=="function")?r:a}function t(a,r){if(typeof r!="function"&&r!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof r);a.prototype=Object.create(r&&r.prototype,{constructor:{value:a,enumerable:!1,writable:!0,configurable:!0}}),r&&(Object.setPrototypeOf?Object.setPrototypeOf(a,r):a.__proto__=r)}var u=["height","width"],l=function(a){t(r,a);function r(){return o(this,r),e(this,(r.__proto__||Object.getPrototypeOf(r)).apply(this,arguments))}return O(r,[{key:"format",value:function(f,n){u.indexOf(f)>-1?n?this.domNode.setAttribute(f,n):this.domNode.removeAttribute(f):m(r.prototype.__proto__||Object.getPrototypeOf(r.prototype),"format",this).call(this,f,n)}}],[{key:"create",value:function(f){var n=m(r.__proto__||Object.getPrototypeOf(r),"create",this).call(this,f);return n.setAttribute("frameborder","0"),n.setAttribute("allowfullscreen",!0),n.setAttribute("src",this.sanitize(f)),n}},{key:"formats",value:function(f){return u.reduce(function(n,h){return f.hasAttribute(h)&&(n[h]=f.getAttribute(h)),n},{})}},{key:"sanitize",value:function(f){return y.default.sanitize(f)}},{key:"value",value:function(f){return f.getAttribute("src")}}]),r}(b.BlockEmbed);l.blotName="video",l.className="ql-video",l.tagName="IFRAME",v.default=l},function(g,v,c){Object.defineProperty(v,"__esModule",{value:!0}),v.default=v.FormulaBlot=void 0;var O=function(){function f(n,h){for(var w=0;w<h.length;w++){var A=h[w];A.enumerable=A.enumerable||!1,A.configurable=!0,"value"in A&&(A.writable=!0),Object.defineProperty(n,A.key,A)}}return function(n,h,w){return h&&f(n.prototype,h),w&&f(n,w),n}}(),m=function f(n,h,w){n===null&&(n=Function.prototype);var A=Object.getOwnPropertyDescriptor(n,h);if(A===void 0){var k=Object.getPrototypeOf(n);return k===null?void 0:f(k,h,w)}else{if("value"in A)return A.value;var S=A.get;return S===void 0?void 0:S.call(w)}},b=c(35),p=t(b),y=c(5),s=t(y),o=c(9),e=t(o);function t(f){return f&&f.__esModule?f:{default:f}}function u(f,n){if(!(f instanceof n))throw new TypeError("Cannot call a class as a function")}function l(f,n){if(!f)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return n&&(typeof n=="object"||typeof n=="function")?n:f}function a(f,n){if(typeof n!="function"&&n!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof n);f.prototype=Object.create(n&&n.prototype,{constructor:{value:f,enumerable:!1,writable:!0,configurable:!0}}),n&&(Object.setPrototypeOf?Object.setPrototypeOf(f,n):f.__proto__=n)}var r=function(f){a(n,f);function n(){return u(this,n),l(this,(n.__proto__||Object.getPrototypeOf(n)).apply(this,arguments))}return O(n,null,[{key:"create",value:function(w){var A=m(n.__proto__||Object.getPrototypeOf(n),"create",this).call(this,w);return typeof w=="string"&&(window.katex.render(w,A,{throwOnError:!1,errorColor:"#f00"}),A.setAttribute("data-value",w)),A}},{key:"value",value:function(w){return w.getAttribute("data-value")}}]),n}(p.default);r.blotName="formula",r.className="ql-formula",r.tagName="SPAN";var i=function(f){a(n,f),O(n,null,[{key:"register",value:function(){s.default.register(r,!0)}}]);function n(){u(this,n);var h=l(this,(n.__proto__||Object.getPrototypeOf(n)).call(this));if(window.katex==null)throw new Error("Formula module requires KaTeX.");return h}return n}(e.default);v.FormulaBlot=r,v.default=i},function(g,v,c){Object.defineProperty(v,"__esModule",{value:!0}),v.default=v.CodeToken=v.CodeBlock=void 0;var O=function(){function w(A,k){for(var S=0;S<k.length;S++){var E=k[S];E.enumerable=E.enumerable||!1,E.configurable=!0,"value"in E&&(E.writable=!0),Object.defineProperty(A,E.key,E)}}return function(A,k,S){return k&&w(A.prototype,k),S&&w(A,S),A}}(),m=function w(A,k,S){A===null&&(A=Function.prototype);var E=Object.getOwnPropertyDescriptor(A,k);if(E===void 0){var d=Object.getPrototypeOf(A);return d===null?void 0:w(d,k,S)}else{if("value"in E)return E.value;var _=E.get;return _===void 0?void 0:_.call(S)}},b=c(0),p=l(b),y=c(5),s=l(y),o=c(9),e=l(o),t=c(13),u=l(t);function l(w){return w&&w.__esModule?w:{default:w}}function a(w,A){if(!(w instanceof A))throw new TypeError("Cannot call a class as a function")}function r(w,A){if(!w)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return A&&(typeof A=="object"||typeof A=="function")?A:w}function i(w,A){if(typeof A!="function"&&A!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof A);w.prototype=Object.create(A&&A.prototype,{constructor:{value:w,enumerable:!1,writable:!0,configurable:!0}}),A&&(Object.setPrototypeOf?Object.setPrototypeOf(w,A):w.__proto__=A)}var f=function(w){i(A,w);function A(){return a(this,A),r(this,(A.__proto__||Object.getPrototypeOf(A)).apply(this,arguments))}return O(A,[{key:"replaceWith",value:function(S){this.domNode.textContent=this.domNode.textContent,this.attach(),m(A.prototype.__proto__||Object.getPrototypeOf(A.prototype),"replaceWith",this).call(this,S)}},{key:"highlight",value:function(S){var E=this.domNode.textContent;this.cachedText!==E&&((E.trim().length>0||this.cachedText==null)&&(this.domNode.innerHTML=S(E),this.domNode.normalize(),this.attach()),this.cachedText=E)}}]),A}(u.default);f.className="ql-syntax";var n=new p.default.Attributor.Class("token","hljs",{scope:p.default.Scope.INLINE}),h=function(w){i(A,w),O(A,null,[{key:"register",value:function(){s.default.register(n,!0),s.default.register(f,!0)}}]);function A(k,S){a(this,A);var E=r(this,(A.__proto__||Object.getPrototypeOf(A)).call(this,k,S));if(typeof E.options.highlight!="function")throw new Error("Syntax module requires highlight.js. Please include the library on the page before Quill.");var d=null;return E.quill.on(s.default.events.SCROLL_OPTIMIZE,function(){clearTimeout(d),d=setTimeout(function(){E.highlight(),d=null},E.options.interval)}),E.highlight(),E}return O(A,[{key:"highlight",value:function(){var S=this;if(!this.quill.selection.composing){this.quill.update(s.default.sources.USER);var E=this.quill.getSelection();this.quill.scroll.descendants(f).forEach(function(d){d.highlight(S.options.highlight)}),this.quill.update(s.default.sources.SILENT),E!=null&&this.quill.setSelection(E,s.default.sources.SILENT)}}}]),A}(e.default);h.DEFAULTS={highlight:function(){return window.hljs==null?null:function(w){var A=window.hljs.highlightAuto(w);return A.value}}(),interval:1e3},v.CodeBlock=f,v.CodeToken=n,v.default=h},function(g,v){g.exports='<svg viewbox="0 0 18 18"> <line class=ql-stroke x1=3 x2=15 y1=9 y2=9></line> <line class=ql-stroke x1=3 x2=13 y1=14 y2=14></line> <line class=ql-stroke x1=3 x2=9 y1=4 y2=4></line> </svg>'},function(g,v){g.exports='<svg viewbox="0 0 18 18"> <line class=ql-stroke x1=15 x2=3 y1=9 y2=9></line> <line class=ql-stroke x1=14 x2=4 y1=14 y2=14></line> <line class=ql-stroke x1=12 x2=6 y1=4 y2=4></line> </svg>'},function(g,v){g.exports='<svg viewbox="0 0 18 18"> <line class=ql-stroke x1=15 x2=3 y1=9 y2=9></line> <line class=ql-stroke x1=15 x2=5 y1=14 y2=14></line> <line class=ql-stroke x1=15 x2=9 y1=4 y2=4></line> </svg>'},function(g,v){g.exports='<svg viewbox="0 0 18 18"> <line class=ql-stroke x1=15 x2=3 y1=9 y2=9></line> <line class=ql-stroke x1=15 x2=3 y1=14 y2=14></line> <line class=ql-stroke x1=15 x2=3 y1=4 y2=4></line> </svg>'},function(g,v){g.exports='<svg viewbox="0 0 18 18"> <g class="ql-fill ql-color-label"> <polygon points="6 6.868 6 6 5 6 5 7 5.942 7 6 6.868"></polygon> <rect height=1 width=1 x=4 y=4></rect> <polygon points="6.817 5 6 5 6 6 6.38 6 6.817 5"></polygon> <rect height=1 width=1 x=2 y=6></rect> <rect height=1 width=1 x=3 y=5></rect> <rect height=1 width=1 x=4 y=7></rect> <polygon points="4 11.439 4 11 3 11 3 12 3.755 12 4 11.439"></polygon> <rect height=1 width=1 x=2 y=12></rect> <rect height=1 width=1 x=2 y=9></rect> <rect height=1 width=1 x=2 y=15></rect> <polygon points="4.63 10 4 10 4 11 4.192 11 4.63 10"></polygon> <rect height=1 width=1 x=3 y=8></rect> <path d=M10.832,4.2L11,4.582V4H10.708A1.948,1.948,0,0,1,10.832,4.2Z></path> <path d=M7,4.582L7.168,4.2A1.929,1.929,0,0,1,7.292,4H7V4.582Z></path> <path d=M8,13H7.683l-0.351.8a1.933,1.933,0,0,1-.124.2H8V13Z></path> <rect height=1 width=1 x=12 y=2></rect> <rect height=1 width=1 x=11 y=3></rect> <path d=M9,3H8V3.282A1.985,1.985,0,0,1,9,3Z></path> <rect height=1 width=1 x=2 y=3></rect> <rect height=1 width=1 x=6 y=2></rect> <rect height=1 width=1 x=3 y=2></rect> <rect height=1 width=1 x=5 y=3></rect> <rect height=1 width=1 x=9 y=2></rect> <rect height=1 width=1 x=15 y=14></rect> <polygon points="13.447 10.174 13.469 10.225 13.472 10.232 13.808 11 14 11 14 10 13.37 10 13.447 10.174"></polygon> <rect height=1 width=1 x=13 y=7></rect> <rect height=1 width=1 x=15 y=5></rect> <rect height=1 width=1 x=14 y=6></rect> <rect height=1 width=1 x=15 y=8></rect> <rect height=1 width=1 x=14 y=9></rect> <path d=M3.775,14H3v1H4V14.314A1.97,1.97,0,0,1,3.775,14Z></path> <rect height=1 width=1 x=14 y=3></rect> <polygon points="12 6.868 12 6 11.62 6 12 6.868"></polygon> <rect height=1 width=1 x=15 y=2></rect> <rect height=1 width=1 x=12 y=5></rect> <rect height=1 width=1 x=13 y=4></rect> <polygon points="12.933 9 13 9 13 8 12.495 8 12.933 9"></polygon> <rect height=1 width=1 x=9 y=14></rect> <rect height=1 width=1 x=8 y=15></rect> <path d=M6,14.926V15H7V14.316A1.993,1.993,0,0,1,6,14.926Z></path> <rect height=1 width=1 x=5 y=15></rect> <path d=M10.668,13.8L10.317,13H10v1h0.792A1.947,1.947,0,0,1,10.668,13.8Z></path> <rect height=1 width=1 x=11 y=15></rect> <path d=M14.332,12.2a1.99,1.99,0,0,1,.166.8H15V12H14.245Z></path> <rect height=1 width=1 x=14 y=15></rect> <rect height=1 width=1 x=15 y=11></rect> </g> <polyline class=ql-stroke points="5.5 13 9 5 12.5 13"></polyline> <line class=ql-stroke x1=11.63 x2=6.38 y1=11 y2=11></line> </svg>'},function(g,v){g.exports='<svg viewbox="0 0 18 18"> <rect class="ql-fill ql-stroke" height=3 width=3 x=4 y=5></rect> <rect class="ql-fill ql-stroke" height=3 width=3 x=11 y=5></rect> <path class="ql-even ql-fill ql-stroke" d=M7,8c0,4.031-3,5-3,5></path> <path class="ql-even ql-fill ql-stroke" d=M14,8c0,4.031-3,5-3,5></path> </svg>'},function(g,v){g.exports='<svg viewbox="0 0 18 18"> <path class=ql-stroke d=M5,4H9.5A2.5,2.5,0,0,1,12,6.5v0A2.5,2.5,0,0,1,9.5,9H5A0,0,0,0,1,5,9V4A0,0,0,0,1,5,4Z></path> <path class=ql-stroke d=M5,9h5.5A2.5,2.5,0,0,1,13,11.5v0A2.5,2.5,0,0,1,10.5,14H5a0,0,0,0,1,0,0V9A0,0,0,0,1,5,9Z></path> </svg>'},function(g,v){g.exports='<svg class="" viewbox="0 0 18 18"> <line class=ql-stroke x1=5 x2=13 y1=3 y2=3></line> <line class=ql-stroke x1=6 x2=9.35 y1=12 y2=3></line> <line class=ql-stroke x1=11 x2=15 y1=11 y2=15></line> <line class=ql-stroke x1=15 x2=11 y1=11 y2=15></line> <rect class=ql-fill height=1 rx=0.5 ry=0.5 width=7 x=2 y=14></rect> </svg>'},function(g,v){g.exports='<svg viewbox="0 0 18 18"> <line class="ql-color-label ql-stroke ql-transparent" x1=3 x2=15 y1=15 y2=15></line> <polyline class=ql-stroke points="5.5 11 9 3 12.5 11"></polyline> <line class=ql-stroke x1=11.63 x2=6.38 y1=9 y2=9></line> </svg>'},function(g,v){g.exports='<svg viewbox="0 0 18 18"> <polygon class="ql-stroke ql-fill" points="3 11 5 9 3 7 3 11"></polygon> <line class="ql-stroke ql-fill" x1=15 x2=11 y1=4 y2=4></line> <path class=ql-fill d=M11,3a3,3,0,0,0,0,6h1V3H11Z></path> <rect class=ql-fill height=11 width=1 x=11 y=4></rect> <rect class=ql-fill height=11 width=1 x=13 y=4></rect> </svg>'},function(g,v){g.exports='<svg viewbox="0 0 18 18"> <polygon class="ql-stroke ql-fill" points="15 12 13 10 15 8 15 12"></polygon> <line class="ql-stroke ql-fill" x1=9 x2=5 y1=4 y2=4></line> <path class=ql-fill d=M5,3A3,3,0,0,0,5,9H6V3H5Z></path> <rect class=ql-fill height=11 width=1 x=5 y=4></rect> <rect class=ql-fill height=11 width=1 x=7 y=4></rect> </svg>'},function(g,v){g.exports='<svg viewbox="0 0 18 18"> <path class=ql-fill d=M14,16H4a1,1,0,0,1,0-2H14A1,1,0,0,1,14,16Z /> <path class=ql-fill d=M14,4H4A1,1,0,0,1,4,2H14A1,1,0,0,1,14,4Z /> <rect class=ql-fill x=3 y=6 width=12 height=6 rx=1 ry=1 /> </svg>'},function(g,v){g.exports='<svg viewbox="0 0 18 18"> <path class=ql-fill d=M13,16H5a1,1,0,0,1,0-2h8A1,1,0,0,1,13,16Z /> <path class=ql-fill d=M13,4H5A1,1,0,0,1,5,2h8A1,1,0,0,1,13,4Z /> <rect class=ql-fill x=2 y=6 width=14 height=6 rx=1 ry=1 /> </svg>'},function(g,v){g.exports='<svg viewbox="0 0 18 18"> <path class=ql-fill d=M15,8H13a1,1,0,0,1,0-2h2A1,1,0,0,1,15,8Z /> <path class=ql-fill d=M15,12H13a1,1,0,0,1,0-2h2A1,1,0,0,1,15,12Z /> <path class=ql-fill d=M15,16H5a1,1,0,0,1,0-2H15A1,1,0,0,1,15,16Z /> <path class=ql-fill d=M15,4H5A1,1,0,0,1,5,2H15A1,1,0,0,1,15,4Z /> <rect class=ql-fill x=2 y=6 width=8 height=6 rx=1 ry=1 /> </svg>'},function(g,v){g.exports='<svg viewbox="0 0 18 18"> <path class=ql-fill d=M5,8H3A1,1,0,0,1,3,6H5A1,1,0,0,1,5,8Z /> <path class=ql-fill d=M5,12H3a1,1,0,0,1,0-2H5A1,1,0,0,1,5,12Z /> <path class=ql-fill d=M13,16H3a1,1,0,0,1,0-2H13A1,1,0,0,1,13,16Z /> <path class=ql-fill d=M13,4H3A1,1,0,0,1,3,2H13A1,1,0,0,1,13,4Z /> <rect class=ql-fill x=8 y=6 width=8 height=6 rx=1 ry=1 transform="translate(24 18) rotate(-180)"/> </svg>'},function(g,v){g.exports='<svg viewbox="0 0 18 18"> <path class=ql-fill d=M11.759,2.482a2.561,2.561,0,0,0-3.53.607A7.656,7.656,0,0,0,6.8,6.2C6.109,9.188,5.275,14.677,4.15,14.927a1.545,1.545,0,0,0-1.3-.933A0.922,0.922,0,0,0,2,15.036S1.954,16,4.119,16s3.091-2.691,3.7-5.553c0.177-.826.36-1.726,0.554-2.6L8.775,6.2c0.381-1.421.807-2.521,1.306-2.676a1.014,1.014,0,0,0,1.02.56A0.966,0.966,0,0,0,11.759,2.482Z></path> <rect class=ql-fill height=1.6 rx=0.8 ry=0.8 width=5 x=5.15 y=6.2></rect> <path class=ql-fill d=M13.663,12.027a1.662,1.662,0,0,1,.266-0.276q0.193,0.069.456,0.138a2.1,2.1,0,0,0,.535.069,1.075,1.075,0,0,0,.767-0.3,1.044,1.044,0,0,0,.314-0.8,0.84,0.84,0,0,0-.238-0.619,0.8,0.8,0,0,0-.594-0.239,1.154,1.154,0,0,0-.781.3,4.607,4.607,0,0,0-.781,1q-0.091.15-.218,0.346l-0.246.38c-0.068-.288-0.137-0.582-0.212-0.885-0.459-1.847-2.494-.984-2.941-0.8-0.482.2-.353,0.647-0.094,0.529a0.869,0.869,0,0,1,1.281.585c0.217,0.751.377,1.436,0.527,2.038a5.688,5.688,0,0,1-.362.467,2.69,2.69,0,0,1-.264.271q-0.221-.08-0.471-0.147a2.029,2.029,0,0,0-.522-0.066,1.079,1.079,0,0,0-.768.3A1.058,1.058,0,0,0,9,15.131a0.82,0.82,0,0,0,.832.852,1.134,1.134,0,0,0,.787-0.3,5.11,5.11,0,0,0,.776-0.993q0.141-.219.215-0.34c0.046-.076.122-0.194,0.223-0.346a2.786,2.786,0,0,0,.918,1.726,2.582,2.582,0,0,0,2.376-.185c0.317-.181.212-0.565,0-0.494A0.807,0.807,0,0,1,14.176,15a5.159,5.159,0,0,1-.913-2.446l0,0Q13.487,12.24,13.663,12.027Z></path> </svg>'},function(g,v){g.exports='<svg viewBox="0 0 18 18"> <path class=ql-fill d=M10,4V14a1,1,0,0,1-2,0V10H3v4a1,1,0,0,1-2,0V4A1,1,0,0,1,3,4V8H8V4a1,1,0,0,1,2,0Zm6.06787,9.209H14.98975V7.59863a.54085.54085,0,0,0-.605-.60547h-.62744a1.01119,1.01119,0,0,0-.748.29688L11.645,8.56641a.5435.5435,0,0,0-.022.8584l.28613.30762a.53861.53861,0,0,0,.84717.0332l.09912-.08789a1.2137,1.2137,0,0,0,.2417-.35254h.02246s-.01123.30859-.01123.60547V13.209H12.041a.54085.54085,0,0,0-.605.60547v.43945a.54085.54085,0,0,0,.605.60547h4.02686a.54085.54085,0,0,0,.605-.60547v-.43945A.54085.54085,0,0,0,16.06787,13.209Z /> </svg>'},function(g,v){g.exports='<svg viewBox="0 0 18 18"> <path class=ql-fill d=M16.73975,13.81445v.43945a.54085.54085,0,0,1-.605.60547H11.855a.58392.58392,0,0,1-.64893-.60547V14.0127c0-2.90527,3.39941-3.42187,3.39941-4.55469a.77675.77675,0,0,0-.84717-.78125,1.17684,1.17684,0,0,0-.83594.38477c-.2749.26367-.561.374-.85791.13184l-.4292-.34082c-.30811-.24219-.38525-.51758-.1543-.81445a2.97155,2.97155,0,0,1,2.45361-1.17676,2.45393,2.45393,0,0,1,2.68408,2.40918c0,2.45312-3.1792,2.92676-3.27832,3.93848h2.79443A.54085.54085,0,0,1,16.73975,13.81445ZM9,3A.99974.99974,0,0,0,8,4V8H3V4A1,1,0,0,0,1,4V14a1,1,0,0,0,2,0V10H8v4a1,1,0,0,0,2,0V4A.99974.99974,0,0,0,9,3Z /> </svg>'},function(g,v){g.exports='<svg viewbox="0 0 18 18"> <line class=ql-stroke x1=7 x2=13 y1=4 y2=4></line> <line class=ql-stroke x1=5 x2=11 y1=14 y2=14></line> <line class=ql-stroke x1=8 x2=10 y1=14 y2=4></line> </svg>'},function(g,v){g.exports='<svg viewbox="0 0 18 18"> <rect class=ql-stroke height=10 width=12 x=3 y=4></rect> <circle class=ql-fill cx=6 cy=7 r=1></circle> <polyline class="ql-even ql-fill" points="5 12 5 11 7 9 8 10 11 7 13 9 13 12 5 12"></polyline> </svg>'},function(g,v){g.exports='<svg viewbox="0 0 18 18"> <line class=ql-stroke x1=3 x2=15 y1=14 y2=14></line> <line class=ql-stroke x1=3 x2=15 y1=4 y2=4></line> <line class=ql-stroke x1=9 x2=15 y1=9 y2=9></line> <polyline class="ql-fill ql-stroke" points="3 7 3 11 5 9 3 7"></polyline> </svg>'},function(g,v){g.exports='<svg viewbox="0 0 18 18"> <line class=ql-stroke x1=3 x2=15 y1=14 y2=14></line> <line class=ql-stroke x1=3 x2=15 y1=4 y2=4></line> <line class=ql-stroke x1=9 x2=15 y1=9 y2=9></line> <polyline class=ql-stroke points="5 7 5 11 3 9 5 7"></polyline> </svg>'},function(g,v){g.exports='<svg viewbox="0 0 18 18"> <line class=ql-stroke x1=7 x2=11 y1=7 y2=11></line> <path class="ql-even ql-stroke" d=M8.9,4.577a3.476,3.476,0,0,1,.36,4.679A3.476,3.476,0,0,1,4.577,8.9C3.185,7.5,2.035,6.4,4.217,4.217S7.5,3.185,8.9,4.577Z></path> <path class="ql-even ql-stroke" d=M13.423,9.1a3.476,3.476,0,0,0-4.679-.36,3.476,3.476,0,0,0,.36,4.679c1.392,1.392,2.5,2.542,4.679.36S14.815,10.5,13.423,9.1Z></path> </svg>'},function(g,v){g.exports='<svg viewbox="0 0 18 18"> <line class=ql-stroke x1=7 x2=15 y1=4 y2=4></line> <line class=ql-stroke x1=7 x2=15 y1=9 y2=9></line> <line class=ql-stroke x1=7 x2=15 y1=14 y2=14></line> <line class="ql-stroke ql-thin" x1=2.5 x2=4.5 y1=5.5 y2=5.5></line> <path class=ql-fill d=M3.5,6A0.5,0.5,0,0,1,3,5.5V3.085l-0.276.138A0.5,0.5,0,0,1,2.053,3c-0.124-.247-0.023-0.324.224-0.447l1-.5A0.5,0.5,0,0,1,4,2.5v3A0.5,0.5,0,0,1,3.5,6Z></path> <path class="ql-stroke ql-thin" d=M4.5,10.5h-2c0-.234,1.85-1.076,1.85-2.234A0.959,0.959,0,0,0,2.5,8.156></path> <path class="ql-stroke ql-thin" d=M2.5,14.846a0.959,0.959,0,0,0,1.85-.109A0.7,0.7,0,0,0,3.75,14a0.688,0.688,0,0,0,.6-0.736,0.959,0.959,0,0,0-1.85-.109></path> </svg>'},function(g,v){g.exports='<svg viewbox="0 0 18 18"> <line class=ql-stroke x1=6 x2=15 y1=4 y2=4></line> <line class=ql-stroke x1=6 x2=15 y1=9 y2=9></line> <line class=ql-stroke x1=6 x2=15 y1=14 y2=14></line> <line class=ql-stroke x1=3 x2=3 y1=4 y2=4></line> <line class=ql-stroke x1=3 x2=3 y1=9 y2=9></line> <line class=ql-stroke x1=3 x2=3 y1=14 y2=14></line> </svg>'},function(g,v){g.exports='<svg class="" viewbox="0 0 18 18"> <line class=ql-stroke x1=9 x2=15 y1=4 y2=4></line> <polyline class=ql-stroke points="3 4 4 5 6 3"></polyline> <line class=ql-stroke x1=9 x2=15 y1=14 y2=14></line> <polyline class=ql-stroke points="3 14 4 15 6 13"></polyline> <line class=ql-stroke x1=9 x2=15 y1=9 y2=9></line> <polyline class=ql-stroke points="3 9 4 10 6 8"></polyline> </svg>'},function(g,v){g.exports='<svg viewbox="0 0 18 18"> <path class=ql-fill d=M15.5,15H13.861a3.858,3.858,0,0,0,1.914-2.975,1.8,1.8,0,0,0-1.6-1.751A1.921,1.921,0,0,0,12.021,11.7a0.50013,0.50013,0,1,0,.957.291h0a0.914,0.914,0,0,1,1.053-.725,0.81,0.81,0,0,1,.744.762c0,1.076-1.16971,1.86982-1.93971,2.43082A1.45639,1.45639,0,0,0,12,15.5a0.5,0.5,0,0,0,.5.5h3A0.5,0.5,0,0,0,15.5,15Z /> <path class=ql-fill d=M9.65,5.241a1,1,0,0,0-1.409.108L6,7.964,3.759,5.349A1,1,0,0,0,2.192,6.59178Q2.21541,6.6213,2.241,6.649L4.684,9.5,2.241,12.35A1,1,0,0,0,3.71,13.70722q0.02557-.02768.049-0.05722L6,11.036,8.241,13.65a1,1,0,1,0,1.567-1.24277Q9.78459,12.3777,9.759,12.35L7.316,9.5,9.759,6.651A1,1,0,0,0,9.65,5.241Z /> </svg>'},function(g,v){g.exports='<svg viewbox="0 0 18 18"> <path class=ql-fill d=M15.5,7H13.861a4.015,4.015,0,0,0,1.914-2.975,1.8,1.8,0,0,0-1.6-1.751A1.922,1.922,0,0,0,12.021,3.7a0.5,0.5,0,1,0,.957.291,0.917,0.917,0,0,1,1.053-.725,0.81,0.81,0,0,1,.744.762c0,1.077-1.164,1.925-1.934,2.486A1.423,1.423,0,0,0,12,7.5a0.5,0.5,0,0,0,.5.5h3A0.5,0.5,0,0,0,15.5,7Z /> <path class=ql-fill d=M9.651,5.241a1,1,0,0,0-1.41.108L6,7.964,3.759,5.349a1,1,0,1,0-1.519,1.3L4.683,9.5,2.241,12.35a1,1,0,1,0,1.519,1.3L6,11.036,8.241,13.65a1,1,0,0,0,1.519-1.3L7.317,9.5,9.759,6.651A1,1,0,0,0,9.651,5.241Z /> </svg>'},function(g,v){g.exports='<svg viewbox="0 0 18 18"> <line class="ql-stroke ql-thin" x1=15.5 x2=2.5 y1=8.5 y2=9.5></line> <path class=ql-fill d=M9.007,8C6.542,7.791,6,7.519,6,6.5,6,5.792,7.283,5,9,5c1.571,0,2.765.679,2.969,1.309a1,1,0,0,0,1.9-.617C13.356,4.106,11.354,3,9,3,6.2,3,4,4.538,4,6.5a3.2,3.2,0,0,0,.5,1.843Z></path> <path class=ql-fill d=M8.984,10C11.457,10.208,12,10.479,12,11.5c0,0.708-1.283,1.5-3,1.5-1.571,0-2.765-.679-2.969-1.309a1,1,0,1,0-1.9.617C4.644,13.894,6.646,15,9,15c2.8,0,5-1.538,5-3.5a3.2,3.2,0,0,0-.5-1.843Z></path> </svg>'},function(g,v){g.exports='<svg viewbox="0 0 18 18"> <path class=ql-stroke d=M5,3V9a4.012,4.012,0,0,0,4,4H9a4.012,4.012,0,0,0,4-4V3></path> <rect class=ql-fill height=1 rx=0.5 ry=0.5 width=12 x=3 y=15></rect> </svg>'},function(g,v){g.exports='<svg viewbox="0 0 18 18"> <rect class=ql-stroke height=12 width=12 x=3 y=3></rect> <rect class=ql-fill height=12 width=1 x=5 y=3></rect> <rect class=ql-fill height=12 width=1 x=12 y=3></rect> <rect class=ql-fill height=2 width=8 x=5 y=8></rect> <rect class=ql-fill height=1 width=3 x=3 y=5></rect> <rect class=ql-fill height=1 width=3 x=3 y=7></rect> <rect class=ql-fill height=1 width=3 x=3 y=10></rect> <rect class=ql-fill height=1 width=3 x=3 y=12></rect> <rect class=ql-fill height=1 width=3 x=12 y=5></rect> <rect class=ql-fill height=1 width=3 x=12 y=7></rect> <rect class=ql-fill height=1 width=3 x=12 y=10></rect> <rect class=ql-fill height=1 width=3 x=12 y=12></rect> </svg>'},function(g,v){g.exports='<svg viewbox="0 0 18 18"> <polygon class=ql-stroke points="7 11 9 13 11 11 7 11"></polygon> <polygon class=ql-stroke points="7 7 9 5 11 7 7 7"></polygon> </svg>'},function(g,v,c){Object.defineProperty(v,"__esModule",{value:!0}),v.default=v.BubbleTooltip=void 0;var O=function A(k,S,E){k===null&&(k=Function.prototype);var d=Object.getOwnPropertyDescriptor(k,S);if(d===void 0){var _=Object.getPrototypeOf(k);return _===null?void 0:A(_,S,E)}else{if("value"in d)return d.value;var N=d.get;return N===void 0?void 0:N.call(E)}},m=function(){function A(k,S){for(var E=0;E<S.length;E++){var d=S[E];d.enumerable=d.enumerable||!1,d.configurable=!0,"value"in d&&(d.writable=!0),Object.defineProperty(k,d.key,d)}}return function(k,S,E){return S&&A(k.prototype,S),E&&A(k,E),k}}(),b=c(3),p=a(b),y=c(8),s=a(y),o=c(43),e=a(o),t=c(15),u=c(41),l=a(u);function a(A){return A&&A.__esModule?A:{default:A}}function r(A,k){if(!(A instanceof k))throw new TypeError("Cannot call a class as a function")}function i(A,k){if(!A)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return k&&(typeof k=="object"||typeof k=="function")?k:A}function f(A,k){if(typeof k!="function"&&k!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof k);A.prototype=Object.create(k&&k.prototype,{constructor:{value:A,enumerable:!1,writable:!0,configurable:!0}}),k&&(Object.setPrototypeOf?Object.setPrototypeOf(A,k):A.__proto__=k)}var n=[["bold","italic","link"],[{header:1},{header:2},"blockquote"]],h=function(A){f(k,A);function k(S,E){r(this,k),E.modules.toolbar!=null&&E.modules.toolbar.container==null&&(E.modules.toolbar.container=n);var d=i(this,(k.__proto__||Object.getPrototypeOf(k)).call(this,S,E));return d.quill.container.classList.add("ql-bubble"),d}return m(k,[{key:"extendToolbar",value:function(E){this.tooltip=new w(this.quill,this.options.bounds),this.tooltip.root.appendChild(E.container),this.buildButtons([].slice.call(E.container.querySelectorAll("button")),l.default),this.buildPickers([].slice.call(E.container.querySelectorAll("select")),l.default)}}]),k}(e.default);h.DEFAULTS=(0,p.default)(!0,{},e.default.DEFAULTS,{modules:{toolbar:{handlers:{link:function(k){k?this.quill.theme.tooltip.edit():this.quill.format("link",!1)}}}}});var w=function(A){f(k,A);function k(S,E){r(this,k);var d=i(this,(k.__proto__||Object.getPrototypeOf(k)).call(this,S,E));return d.quill.on(s.default.events.EDITOR_CHANGE,function(_,N,x,R){if(_===s.default.events.SELECTION_CHANGE)if(N!=null&&N.length>0&&R===s.default.sources.USER){d.show(),d.root.style.left="0px",d.root.style.width="",d.root.style.width=d.root.offsetWidth+"px";var F=d.quill.getLines(N.index,N.length);if(F.length===1)d.position(d.quill.getBounds(N));else{var U=F[F.length-1],W=d.quill.getIndex(U),D=Math.min(U.length()-1,N.index+N.length-W),M=d.quill.getBounds(new t.Range(W,D));d.position(M)}}else document.activeElement!==d.textbox&&d.quill.hasFocus()&&d.hide()}),d}return m(k,[{key:"listen",value:function(){var E=this;O(k.prototype.__proto__||Object.getPrototypeOf(k.prototype),"listen",this).call(this),this.root.querySelector(".ql-close").addEventListener("click",function(){E.root.classList.remove("ql-editing")}),this.quill.on(s.default.events.SCROLL_OPTIMIZE,function(){setTimeout(function(){if(!E.root.classList.contains("ql-hidden")){var d=E.quill.getSelection();d!=null&&E.position(E.quill.getBounds(d))}},1)})}},{key:"cancel",value:function(){this.show()}},{key:"position",value:function(E){var d=O(k.prototype.__proto__||Object.getPrototypeOf(k.prototype),"position",this).call(this,E),_=this.root.querySelector(".ql-tooltip-arrow");if(_.style.marginLeft="",d===0)return d;_.style.marginLeft=-1*d-_.offsetWidth/2+"px"}}]),k}(o.BaseTooltip);w.TEMPLATE=['<span class="ql-tooltip-arrow"></span>','<div class="ql-tooltip-editor">','<input type="text" data-formula="e=mc^2" data-link="https://quilljs.com" data-video="Embed URL">','<a class="ql-close"></a>',"</div>"].join(""),v.BubbleTooltip=w,v.default=h},function(g,v,c){g.exports=c(63)}]).default})})(He);var tt=He.exports;const Ne=Ce(tt);var ve=-1,de=1,ce=0;function be(L,B,g,v){if(L===B)return L?[[ce,L]]:[];if(g!=null){var c=lt(L,B,g);if(c)return c}var O=Pe(L,B),m=L.substring(0,O);L=L.substring(O),B=B.substring(O),O=Le(L,B);var b=L.substring(L.length-O);L=L.substring(0,L.length-O),B=B.substring(0,B.length-O);var p=nt(L,B);return m&&p.unshift([ce,m]),b&&p.push([ce,b]),ze(p,v),p}function nt(L,B){var g;if(!L)return[[de,B]];if(!B)return[[ve,L]];var v=L.length>B.length?L:B,c=L.length>B.length?B:L,O=v.indexOf(c);if(O!==-1)return g=[[de,v.substring(0,O)],[ce,c],[de,v.substring(O+c.length)]],L.length>B.length&&(g[0][0]=g[2][0]=ve),g;if(c.length===1)return[[ve,L],[de,B]];var m=it(L,B);if(m){var b=m[0],p=m[1],y=m[2],s=m[3],o=m[4],e=be(b,y),t=be(p,s);return e.concat([[ce,o]],t)}return rt(L,B)}function rt(L,B){for(var g=L.length,v=B.length,c=Math.ceil((g+v)/2),O=c,m=2*c,b=new Array(m),p=new Array(m),y=0;y<m;y++)b[y]=-1,p[y]=-1;b[O+1]=0,p[O+1]=0;for(var s=g-v,o=s%2!==0,e=0,t=0,u=0,l=0,a=0;a<c;a++){for(var r=-a+e;r<=a-t;r+=2){var i=O+r,f;r===-a||r!==a&&b[i-1]<b[i+1]?f=b[i+1]:f=b[i-1]+1;for(var n=f-r;f<g&&n<v&&L.charAt(f)===B.charAt(n);)f++,n++;if(b[i]=f,f>g)t+=2;else if(n>v)e+=2;else if(o){var h=O+s-r;if(h>=0&&h<m&&p[h]!==-1){var w=g-p[h];if(f>=w)return Me(L,B,f,n)}}}for(var A=-a+u;A<=a-l;A+=2){var h=O+A,w;A===-a||A!==a&&p[h-1]<p[h+1]?w=p[h+1]:w=p[h-1]+1;for(var k=w-A;w<g&&k<v&&L.charAt(g-w-1)===B.charAt(v-k-1);)w++,k++;if(p[h]=w,w>g)l+=2;else if(k>v)u+=2;else if(!o){var i=O+s-A;if(i>=0&&i<m&&b[i]!==-1){var f=b[i],n=O+f-i;if(w=g-w,f>=w)return Me(L,B,f,n)}}}}return[[ve,L],[de,B]]}function Me(L,B,g,v){var c=L.substring(0,g),O=B.substring(0,v),m=L.substring(g),b=B.substring(v),p=be(c,O),y=be(m,b);return p.concat(y)}function Pe(L,B){if(!L||!B||L.charAt(0)!==B.charAt(0))return 0;for(var g=0,v=Math.min(L.length,B.length),c=v,O=0;g<c;)L.substring(O,c)==B.substring(O,c)?(g=c,O=g):v=c,c=Math.floor((v-g)/2+g);return Ke(L.charCodeAt(c-1))&&c--,c}function Le(L,B){if(!L||!B||L.slice(-1)!==B.slice(-1))return 0;for(var g=0,v=Math.min(L.length,B.length),c=v,O=0;g<c;)L.substring(L.length-c,L.length-O)==B.substring(B.length-c,B.length-O)?(g=c,O=g):v=c,c=Math.floor((v-g)/2+g);return Ve(L.charCodeAt(L.length-c))&&c--,c}function it(L,B){var g=L.length>B.length?L:B,v=L.length>B.length?B:L;if(g.length<4||v.length*2<g.length)return null;function c(t,u,l){for(var a=t.substring(l,l+Math.floor(t.length/4)),r=-1,i="",f,n,h,w;(r=u.indexOf(a,r+1))!==-1;){var A=Pe(t.substring(l),u.substring(r)),k=Le(t.substring(0,l),u.substring(0,r));i.length<k+A&&(i=u.substring(r-k,r)+u.substring(r,r+A),f=t.substring(0,l-k),n=t.substring(l+A),h=u.substring(0,r-k),w=u.substring(r+A))}return i.length*2>=t.length?[f,n,h,w,i]:null}var O=c(g,v,Math.ceil(g.length/4)),m=c(g,v,Math.ceil(g.length/2)),b;if(!O&&!m)return null;m?O?b=O[4].length>m[4].length?O:m:b=m:b=O;var p,y,s,o;L.length>B.length?(p=b[0],y=b[1],s=b[2],o=b[3]):(s=b[0],o=b[1],p=b[2],y=b[3]);var e=b[4];return[p,y,s,o,e]}function ze(L,B){L.push([ce,""]);for(var g=0,v=0,c=0,O="",m="",b;g<L.length;){if(g<L.length-1&&!L[g][1]){L.splice(g,1);continue}switch(L[g][0]){case de:c++,m+=L[g][1],g++;break;case ve:v++,O+=L[g][1],g++;break;case ce:var p=g-c-v-1;if(B){if(p>=0&&Ze(L[p][1])){var y=L[p][1].slice(-1);if(L[p][1]=L[p][1].slice(0,-1),O=y+O,m=y+m,!L[p][1]){L.splice(p,1),g--;var s=p-1;L[s]&&L[s][0]===de&&(c++,m=L[s][1]+m,s--),L[s]&&L[s][0]===ve&&(v++,O=L[s][1]+O,s--),p=s}}if($e(L[g][1])){var y=L[g][1].charAt(0);L[g][1]=L[g][1].slice(1),O+=y,m+=y}}if(g<L.length-1&&!L[g][1]){L.splice(g,1);break}if(O.length>0||m.length>0){O.length>0&&m.length>0&&(b=Pe(m,O),b!==0&&(p>=0?L[p][1]+=m.substring(0,b):(L.splice(0,0,[ce,m.substring(0,b)]),g++),m=m.substring(b),O=O.substring(b)),b=Le(m,O),b!==0&&(L[g][1]=m.substring(m.length-b)+L[g][1],m=m.substring(0,m.length-b),O=O.substring(0,O.length-b)));var o=c+v;O.length===0&&m.length===0?(L.splice(g-o,o),g=g-o):O.length===0?(L.splice(g-o,o,[de,m]),g=g-o+1):m.length===0?(L.splice(g-o,o,[ve,O]),g=g-o+1):(L.splice(g-o,o,[ve,O],[de,m]),g=g-o+2)}g!==0&&L[g-1][0]===ce?(L[g-1][1]+=L[g][1],L.splice(g,1)):g++,c=0,v=0,O="",m="";break}}L[L.length-1][1]===""&&L.pop();var e=!1;for(g=1;g<L.length-1;)L[g-1][0]===ce&&L[g+1][0]===ce&&(L[g][1].substring(L[g][1].length-L[g-1][1].length)===L[g-1][1]?(L[g][1]=L[g-1][1]+L[g][1].substring(0,L[g][1].length-L[g-1][1].length),L[g+1][1]=L[g-1][1]+L[g+1][1],L.splice(g-1,1),e=!0):L[g][1].substring(0,L[g+1][1].length)==L[g+1][1]&&(L[g-1][1]+=L[g+1][1],L[g][1]=L[g][1].substring(L[g+1][1].length)+L[g+1][1],L.splice(g+1,1),e=!0)),g++;e&&ze(L,B)}function Ke(L){return L>=55296&&L<=56319}function Ve(L){return L>=56320&&L<=57343}function $e(L){return Ve(L.charCodeAt(0))}function Ze(L){return Ke(L.charCodeAt(L.length-1))}function ot(L){for(var B=[],g=0;g<L.length;g++)L[g][1].length>0&&B.push(L[g]);return B}function Te(L,B,g,v){return Ze(L)||$e(v)?null:ot([[ce,L],[ve,B],[de,g],[ce,v]])}function lt(L,B,g){var v=typeof g=="number"?{index:g,length:0}:g.oldRange,c=typeof g=="number"?null:g.newRange,O=L.length,m=B.length;if(v.length===0&&(c===null||c.length===0)){var b=v.index,p=L.slice(0,b),y=L.slice(b),s=c?c.index:null;e:{var o=b+m-O;if(s!==null&&s!==o||o<0||o>m)break e;var e=B.slice(0,o),t=B.slice(o);if(t!==y)break e;var u=Math.min(b,o),l=p.slice(0,u),a=e.slice(0,u);if(l!==a)break e;var r=p.slice(u),i=e.slice(u);return Te(l,r,i,y)}e:{if(s!==null&&s!==b)break e;var f=b,e=B.slice(0,f),t=B.slice(f);if(e!==p)break e;var n=Math.min(O-f,m-f),h=y.slice(y.length-n),w=t.slice(t.length-n);if(h!==w)break e;var r=y.slice(0,y.length-n),i=t.slice(0,t.length-n);return Te(p,r,i,h)}}if(v.length>0&&c&&c.length===0)e:{var l=L.slice(0,v.index),h=L.slice(v.index+v.length),u=l.length,n=h.length;if(m<u+n)break e;var a=B.slice(0,u),w=B.slice(m-n);if(l!==a||h!==w)break e;var r=L.slice(u,O-n),i=B.slice(u,m-n);return Te(l,r,i,h)}return null}function ke(L,B,g){return be(L,B,g,!0)}ke.INSERT=de;ke.DELETE=ve;ke.EQUAL=ce;var at=ke,qe={},We=ye&&ye.__importDefault||function(L){return L&&L.__esModule?L:{default:L}};Object.defineProperty(qe,"__esModule",{value:!0});var ut=We(Fe),ft=We(Ue),xe;(function(L){function B(O,m,b){O===void 0&&(O={}),m===void 0&&(m={}),typeof O!="object"&&(O={}),typeof m!="object"&&(m={});var p=ut.default(m);b||(p=Object.keys(p).reduce(function(s,o){return p[o]!=null&&(s[o]=p[o]),s},{}));for(var y in O)O[y]!==void 0&&m[y]===void 0&&(p[y]=O[y]);return Object.keys(p).length>0?p:void 0}L.compose=B;function g(O,m){O===void 0&&(O={}),m===void 0&&(m={}),typeof O!="object"&&(O={}),typeof m!="object"&&(m={});var b=Object.keys(O).concat(Object.keys(m)).reduce(function(p,y){return ft.default(O[y],m[y])||(p[y]=m[y]===void 0?null:m[y]),p},{});return Object.keys(b).length>0?b:void 0}L.diff=g;function v(O,m){O===void 0&&(O={}),m===void 0&&(m={}),O=O||{};var b=Object.keys(m).reduce(function(p,y){return m[y]!==O[y]&&O[y]!==void 0&&(p[y]=m[y]),p},{});return Object.keys(O).reduce(function(p,y){return O[y]!==m[y]&&m[y]===void 0&&(p[y]=null),p},b)}L.invert=v;function c(O,m,b){if(b===void 0&&(b=!1),typeof O!="object")return m;if(typeof m=="object"){if(!b)return m;var p=Object.keys(m).reduce(function(y,s){return O[s]===void 0&&(y[s]=m[s]),y},{});return Object.keys(p).length>0?p:void 0}}L.transform=c})(xe||(xe={}));qe.default=xe;var Oe={},Ee={},Ie;function st(){if(Ie)return Ee;Ie=1;var L=ye&&ye.__importDefault||function(v){return v&&v.__esModule?v:{default:v}};Object.defineProperty(Ee,"__esModule",{value:!0});var B=L(Ge()),g=function(){function v(c){this.ops=c,this.index=0,this.offset=0}return v.prototype.hasNext=function(){return this.peekLength()<1/0},v.prototype.next=function(c){c||(c=1/0);var O=this.ops[this.index];if(O){var m=this.offset,b=B.default.length(O);if(c>=b-m?(c=b-m,this.index+=1,this.offset=0):this.offset+=c,typeof O.delete=="number")return{delete:c};var p={};return O.attributes&&(p.attributes=O.attributes),typeof O.retain=="number"?p.retain=c:typeof O.insert=="string"?p.insert=O.insert.substr(m,c):p.insert=O.insert,p}else return{retain:1/0}},v.prototype.peek=function(){return this.ops[this.index]},v.prototype.peekLength=function(){return this.ops[this.index]?B.default.length(this.ops[this.index])-this.offset:1/0},v.prototype.peekType=function(){return this.ops[this.index]?typeof this.ops[this.index].delete=="number"?"delete":typeof this.ops[this.index].retain=="number"?"retain":"insert":"retain"},v.prototype.rest=function(){if(this.hasNext()){if(this.offset===0)return this.ops.slice(this.index);var c=this.offset,O=this.index,m=this.next(),b=this.ops.slice(this.index);return this.offset=c,this.index=O,[m].concat(b)}else return[]},v}();return Ee.default=g,Ee}var De;function Ge(){if(De)return Oe;De=1;var L=ye&&ye.__importDefault||function(v){return v&&v.__esModule?v:{default:v}};Object.defineProperty(Oe,"__esModule",{value:!0});var B=L(st()),g;return function(v){function c(m){return new B.default(m)}v.iterator=c;function O(m){return typeof m.delete=="number"?m.delete:typeof m.retain=="number"?m.retain:typeof m.insert=="string"?m.insert.length:1}v.length=O}(g||(g={})),Oe.default=g,Oe}var _e=ye&&ye.__importDefault||function(L){return L&&L.__esModule?L:{default:L}},Ae=_e(at),ct=_e(Fe),Se=_e(Ue),me=_e(qe),fe=_e(Ge()),ht=String.fromCharCode(0),dt=function(){function L(B){Array.isArray(B)?this.ops=B:B!=null&&Array.isArray(B.ops)?this.ops=B.ops:this.ops=[]}return L.prototype.insert=function(B,g){var v={};return typeof B=="string"&&B.length===0?this:(v.insert=B,g!=null&&typeof g=="object"&&Object.keys(g).length>0&&(v.attributes=g),this.push(v))},L.prototype.delete=function(B){return B<=0?this:this.push({delete:B})},L.prototype.retain=function(B,g){if(B<=0)return this;var v={retain:B};return g!=null&&typeof g=="object"&&Object.keys(g).length>0&&(v.attributes=g),this.push(v)},L.prototype.push=function(B){var g=this.ops.length,v=this.ops[g-1];if(B=ct.default(B),typeof v=="object"){if(typeof B.delete=="number"&&typeof v.delete=="number")return this.ops[g-1]={delete:v.delete+B.delete},this;if(typeof v.delete=="number"&&B.insert!=null&&(g-=1,v=this.ops[g-1],typeof v!="object"))return this.ops.unshift(B),this;if(Se.default(B.attributes,v.attributes)){if(typeof B.insert=="string"&&typeof v.insert=="string")return this.ops[g-1]={insert:v.insert+B.insert},typeof B.attributes=="object"&&(this.ops[g-1].attributes=B.attributes),this;if(typeof B.retain=="number"&&typeof v.retain=="number")return this.ops[g-1]={retain:v.retain+B.retain},typeof B.attributes=="object"&&(this.ops[g-1].attributes=B.attributes),this}}return g===this.ops.length?this.ops.push(B):this.ops.splice(g,0,B),this},L.prototype.chop=function(){var B=this.ops[this.ops.length-1];return B&&B.retain&&!B.attributes&&this.ops.pop(),this},L.prototype.filter=function(B){return this.ops.filter(B)},L.prototype.forEach=function(B){this.ops.forEach(B)},L.prototype.map=function(B){return this.ops.map(B)},L.prototype.partition=function(B){var g=[],v=[];return this.forEach(function(c){var O=B(c)?g:v;O.push(c)}),[g,v]},L.prototype.reduce=function(B,g){return this.ops.reduce(B,g)},L.prototype.changeLength=function(){return this.reduce(function(B,g){return g.insert?B+fe.default.length(g):g.delete?B-g.delete:B},0)},L.prototype.length=function(){return this.reduce(function(B,g){return B+fe.default.length(g)},0)},L.prototype.slice=function(B,g){B===void 0&&(B=0),g===void 0&&(g=1/0);for(var v=[],c=fe.default.iterator(this.ops),O=0;O<g&&c.hasNext();){var m=void 0;O<B?m=c.next(B-O):(m=c.next(g-O),v.push(m)),O+=fe.default.length(m)}return new L(v)},L.prototype.compose=function(B){var g=fe.default.iterator(this.ops),v=fe.default.iterator(B.ops),c=[],O=v.peek();if(O!=null&&typeof O.retain=="number"&&O.attributes==null){for(var m=O.retain;g.peekType()==="insert"&&g.peekLength()<=m;)m-=g.peekLength(),c.push(g.next());O.retain-m>0&&v.next(O.retain-m)}for(var b=new L(c);g.hasNext()||v.hasNext();)if(v.peekType()==="insert")b.push(v.next());else if(g.peekType()==="delete")b.push(g.next());else{var p=Math.min(g.peekLength(),v.peekLength()),y=g.next(p),s=v.next(p);if(typeof s.retain=="number"){var o={};typeof y.retain=="number"?o.retain=p:o.insert=y.insert;var e=me.default.compose(y.attributes,s.attributes,typeof y.retain=="number");if(e&&(o.attributes=e),b.push(o),!v.hasNext()&&Se.default(b.ops[b.ops.length-1],o)){var t=new L(g.rest());return b.concat(t).chop()}}else typeof s.delete=="number"&&typeof y.retain=="number"&&b.push(s)}return b.chop()},L.prototype.concat=function(B){var g=new L(this.ops.slice());return B.ops.length>0&&(g.push(B.ops[0]),g.ops=g.ops.concat(B.ops.slice(1))),g},L.prototype.diff=function(B,g){if(this.ops===B.ops)return new L;var v=[this,B].map(function(p){return p.map(function(y){if(y.insert!=null)return typeof y.insert=="string"?y.insert:ht;var s=p===B?"on":"with";throw new Error("diff() called "+s+" non-document")}).join("")}),c=new L,O=Ae.default(v[0],v[1],g),m=fe.default.iterator(this.ops),b=fe.default.iterator(B.ops);return O.forEach(function(p){for(var y=p[1].length;y>0;){var s=0;switch(p[0]){case Ae.default.INSERT:s=Math.min(b.peekLength(),y),c.push(b.next(s));break;case Ae.default.DELETE:s=Math.min(y,m.peekLength()),m.next(s),c.delete(s);break;case Ae.default.EQUAL:s=Math.min(m.peekLength(),b.peekLength(),y);var o=m.next(s),e=b.next(s);Se.default(o.insert,e.insert)?c.retain(s,me.default.diff(o.attributes,e.attributes)):c.push(e).delete(s);break}y-=s}}),c.chop()},L.prototype.eachLine=function(B,g){g===void 0&&(g=`
`);for(var v=fe.default.iterator(this.ops),c=new L,O=0;v.hasNext();){if(v.peekType()!=="insert")return;var m=v.peek(),b=fe.default.length(m)-v.peekLength(),p=typeof m.insert=="string"?m.insert.indexOf(g,b)-b:-1;if(p<0)c.push(v.next());else if(p>0)c.push(v.next(p));else{if(B(c,v.next(1).attributes||{},O)===!1)return;O+=1,c=new L}}c.length()>0&&B(c,{},O)},L.prototype.invert=function(B){var g=new L;return this.reduce(function(v,c){if(c.insert)g.delete(fe.default.length(c));else{if(c.retain&&c.attributes==null)return g.retain(c.retain),v+c.retain;if(c.delete||c.retain&&c.attributes){var O=c.delete||c.retain,m=B.slice(v,v+O);return m.forEach(function(b){c.delete?g.push(b):c.retain&&c.attributes&&g.retain(fe.default.length(b),me.default.invert(c.attributes,b.attributes))}),v+O}}return v},0),g.chop()},L.prototype.transform=function(B,g){if(g===void 0&&(g=!1),g=!!g,typeof B=="number")return this.transformPosition(B,g);for(var v=B,c=fe.default.iterator(this.ops),O=fe.default.iterator(v.ops),m=new L;c.hasNext()||O.hasNext();)if(c.peekType()==="insert"&&(g||O.peekType()!=="insert"))m.retain(fe.default.length(c.next()));else if(O.peekType()==="insert")m.push(O.next());else{var b=Math.min(c.peekLength(),O.peekLength()),p=c.next(b),y=O.next(b);if(p.delete)continue;y.delete?m.push(y):m.retain(b,me.default.transform(p.attributes,y.attributes,g))}return m.chop()},L.prototype.transformPosition=function(B,g){g===void 0&&(g=!1),g=!!g;for(var v=fe.default.iterator(this.ops),c=0;v.hasNext()&&c<=B;){var O=v.peekLength(),m=v.peekType();if(v.next(),m==="delete"){B-=Math.min(O,B-c);continue}else m==="insert"&&(c<B||!g)&&(B+=O);c+=O}return B},L.Op=fe.default,L.AttributeMap=me.default,L}(),vt=dt;const pt=Ce(vt);/*!
 * VueQuill @vueup/vue-quill v1.2.0
 * https://vueup.github.io/vue-quill/
 * 
 * Includes quill v1.3.7
 * https://quilljs.com/
 * 
 * Copyright (c) 2023 Ahmad Luthfi Masruri
 * Released under the MIT license
 * Date: 2023-05-12T08:44:03.742Z
 */const Be={essential:[[{header:[1,2,3,4,5,6,!1]}],["bold","italic","underline"],[{list:"ordered"},{list:"bullet"},{align:[]}],["blockquote","code-block","link"],[{color:[]},"clean"]],minimal:[[{header:1},{header:2}],["bold","italic","underline"],[{list:"ordered"},{list:"bullet"},{align:[]}]],full:[["bold","italic","underline","strike"],["blockquote","code-block"],[{header:1},{header:2}],[{list:"ordered"},{list:"bullet"}],[{script:"sub"},{script:"super"}],[{indent:"-1"},{indent:"+1"}],[{direction:"rtl"}],[{size:["small",!1,"large","huge"]}],[{header:[1,2,3,4,5,6,!1]}],[{color:[]},{background:[]}],[{font:[]}],[{align:[]}],["link","video","image"],["clean"]]},gt=Qe({name:"QuillEditor",inheritAttrs:!1,props:{content:{type:[String,Object]},contentType:{type:String,default:"delta",validator:L=>["delta","html","text"].includes(L)},enable:{type:Boolean,default:!0},readOnly:{type:Boolean,default:!1},placeholder:{type:String,required:!1},theme:{type:String,default:"snow",validator:L=>["snow","bubble",""].includes(L)},toolbar:{type:[String,Array,Object],required:!1,validator:L=>typeof L=="string"&&L!==""?L.charAt(0)==="#"?!0:Object.keys(Be).indexOf(L)!==-1:!0},modules:{type:Object,required:!1},options:{type:Object,required:!1},globalOptions:{type:Object,required:!1}},emits:["textChange","selectionChange","editorChange","update:content","focus","blur","ready"],setup:(L,B)=>{Xe(()=>{O()}),Je(()=>{g=null});let g,v;const c=Re(),O=()=>{var d;if(c.value){if(v=m(),L.modules)if(Array.isArray(L.modules))for(const _ of L.modules)Ne.register(`modules/${_.name}`,_.module);else Ne.register(`modules/${L.modules.name}`,L.modules.module);g=new Ne(c.value,v),f(L.content),g.on("text-change",o),g.on("selection-change",t),g.on("editor-change",u),L.theme!=="bubble"&&c.value.classList.remove("ql-bubble"),L.theme!=="snow"&&c.value.classList.remove("ql-snow"),(d=g.getModule("toolbar"))===null||d===void 0||d.container.addEventListener("mousedown",_=>{_.preventDefault()}),B.emit("ready",g)}},m=()=>{const d={};if(L.theme!==""&&(d.theme=L.theme),L.readOnly&&(d.readOnly=L.readOnly),L.placeholder&&(d.placeholder=L.placeholder),L.toolbar&&L.toolbar!==""&&(d.modules={toolbar:(()=>{if(typeof L.toolbar=="object")return L.toolbar;if(typeof L.toolbar=="string")return L.toolbar.charAt(0)==="#"?L.toolbar:Be[L.toolbar]})()}),L.modules){const _=(()=>{var N,x;const R={};if(Array.isArray(L.modules))for(const F of L.modules)R[F.name]=(N=F.options)!==null&&N!==void 0?N:{};else R[L.modules.name]=(x=L.modules.options)!==null&&x!==void 0?x:{};return R})();d.modules=Object.assign({},d.modules,_)}return Object.assign({},L.globalOptions,L.options,d)},b=d=>typeof d=="object"&&d?d.slice():d,p=d=>Object.values(d.ops).some(_=>!_.retain||Object.keys(_).length!==1);let y;const s=d=>{if(typeof y==typeof d){if(d===y)return!0;if(typeof d=="object"&&d&&typeof y=="object"&&y)return!p(y.diff(d))}return!1},o=(d,_,N)=>{y=b(i()),s(L.content)||B.emit("update:content",y),B.emit("textChange",{delta:d,oldContents:_,source:N})},e=Re(),t=(d,_,N)=>{e.value=!!(g!=null&&g.hasFocus()),B.emit("selectionChange",{range:d,oldRange:_,source:N})};we(e,d=>{d?B.emit("focus",c):B.emit("blur",c)});const u=(...d)=>{d[0]==="text-change"&&B.emit("editorChange",{name:d[0],delta:d[1],oldContents:d[2],source:d[3]}),d[0]==="selection-change"&&B.emit("editorChange",{name:d[0],range:d[1],oldRange:d[2],source:d[3]})},l=()=>c.value,a=()=>{var d;return(d=g==null?void 0:g.getModule("toolbar"))===null||d===void 0?void 0:d.container},r=()=>{if(g)return g;throw`The quill editor hasn't been instantiated yet,
                  make sure to call this method when the editor ready
                  or use v-on:ready="onReady(quill)" event instead.`},i=(d,_)=>L.contentType==="html"?w():L.contentType==="text"?n(d,_):g==null?void 0:g.getContents(d,_),f=(d,_="api")=>{const N=d||(L.contentType==="delta"?new pt:"");L.contentType==="html"?A(N):L.contentType==="text"?h(N,_):g==null||g.setContents(N,_),y=b(N)},n=(d,_)=>{var N;return(N=g==null?void 0:g.getText(d,_))!==null&&N!==void 0?N:""},h=(d,_="api")=>{g==null||g.setText(d,_)},w=()=>{var d;return(d=g==null?void 0:g.root.innerHTML)!==null&&d!==void 0?d:""},A=d=>{g&&(g.root.innerHTML=d)},k=(d,_="api")=>{const N=g==null?void 0:g.clipboard.convert(d);N&&(g==null||g.setContents(N,_))},S=()=>{g==null||g.focus()},E=()=>{je(()=>{var d;!B.slots.toolbar&&g&&((d=g.getModule("toolbar"))===null||d===void 0||d.container.remove()),O()})};return we(()=>L.content,d=>{if(!g||!d||s(d))return;const _=g.getSelection();_&&je(()=>g==null?void 0:g.setSelection(_)),f(d)},{deep:!0}),we(()=>L.enable,d=>{g&&g.enable(d)}),{editor:c,getEditor:l,getToolbar:a,getQuill:r,getContents:i,setContents:f,getHTML:w,setHTML:A,pasteHTML:k,focus:S,getText:n,setText:h,reinit:E}},render(){var L,B;return[(B=(L=this.$slots).toolbar)===null||B===void 0?void 0:B.call(L),et("div",{ref:"editor",...this.$attrs})]}});export{gt as Q};
