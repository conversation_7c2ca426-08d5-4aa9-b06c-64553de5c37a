import{K as S,T as E,r as m,o as l,c,a as s,u as e,w as _,F as b,Z as D,b as t,t as d,g as w,h as M,A as $,f as h,d as L}from"./app-f4e029a5.js";import{_ as N,b as B}from"./AdminLayout-f619cb63.js";import{_ as V}from"./InputError-00daa197.js";import{_ as q}from"./InputLabel-0f849d64.js";import{P as k}from"./PrimaryButton-faa68167.js";import{M as F}from"./Modal-22d517c7.js";import{Q as U}from"./vue-quill.snow-fc4aedcd.js";import"./_plugin-vue_export-helper-c27b6911.js";/* empty css                                                              */const P={class:"animate-top"},Q=t("h2",{class:"text-2xl font-semibold leading-7 text-gray-900"},"Edit LeadSequence",-1),A={class:"mt-6 shadow ring-1 space-y-1 ring-black ring-opacity-5 sm:rounded-lg bg-white p-6 mb-3"},I={class:"inline-flex items-start space-x-6 justify-start w-full"},K={class:"inline-flex flex-col space-y-1 items-start justify-start w-full"},Z={class:"inline-flex items-center justify-start w-full space-x-2"},z=t("p",{class:"text-sm font-semibold text-gray-900 w-28"},"Scheduled Date: ",-1),G={class:"text-sm leading-6 text-gray-700"},H={class:"inline-flex items-center justify-start w-full space-x-2"},J=t("p",{class:"text-sm font-semibold text-gray-900 w-28"},"Lead:",-1),O={class:"text-sm leading-6 text-gray-700"},R={class:"inline-flex flex-col space-y-1 items-start justify-start w-full"},W={class:"inline-flex items-center justify-start w-full space-x-2"},X=t("span",{class:"text-sm font-semibold text-gray-900 leading-6 w-28"},"Sequence:",-1),Y={class:"text-sm leading-6 text-gray-700"},tt={class:"inline-flex items-center justify-start w-full space-x-2"},et=t("span",{class:"text-sm font-semibold text-gray-900 leading-6 w-28"},"Subject:",-1),st={class:"text-sm leading-6 text-gray-700"},at={class:"bg-white p-4 shadow sm:p-6 sm:rounded-lg border"},ot={class:"flex items-center justify-end"},nt={class:"flex items-center gap-x-4"},it={class:"border-b border-gray-900/10 pb-12"},lt={class:"mt-6 grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-6"},ct={class:"sm:col-span-6"},dt={class:"flex mt-6 items-center justify-between"},rt={class:"ml-auto flex items-center justify-end gap-x-6 mt-4"},_t=t("button",{type:"button",class:"text-sm font-semibold leading-6 text-gray-900"}," Cancel ",-1),mt={key:0,class:"text-sm text-gray-600"},ut={class:"p-6 relative"},pt=t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"},null,-1),xt=[pt],ft=t("h2",{class:"text-lg font-medium text-gray-900"},"Email Tags",-1),ht={class:"mt-4 overflow-x-auto sm:rounded-lg"},gt={class:"w-full text-sm text-left text-gray-500"},yt=t("thead",{class:"text-xs text-gray-700 uppercase bg-gray-50"},[t("tr",null,[t("th",{class:"px-4 py-2 text-gray-900"},"Tag Name"),t("th",{class:"px-4 py-2 text-gray-900"},"Description")])],-1),vt={key:0},bt=t("td",{colspan:"2",class:"px-4 py-4 text-center text-gray-500"}," No Email Tags Found. ",-1),wt=[bt],kt={class:"divide-y divide-gray-300 bg-white"},Tt={class:"px-4 py-2"},jt={class:"flex items-center space-x-2"},Ct=["onClick"],St={key:0,class:"text-green-600 text-xs"},Et={class:"px-4 py-2"},Dt={class:"flex items-center space-x-2"},Qt={__name:"Edit",props:["tags"],setup(g){const i=S().props.data[0],r=E({id:i.id,content:i.content}),T=a=>new Date(a).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric"}),y=m(null),u=m(!1),j=a=>{y.value=a,u.value=!0},v=()=>{u.value=!1,y.value=null},p=m(null),x=m(""),C=(a,o,n)=>{navigator.clipboard.writeText(a).then(()=>{p.value=o,x.value=n,setTimeout(()=>{p.value=null,x.value=""},2e3)})};return(a,o)=>(l(),c(b,null,[s(e(D),{title:"Edit LeadSequence"}),s(N,null,{default:_(()=>[t("div",P,[Q,t("div",A,[t("div",I,[t("div",K,[t("div",Z,[z,t("p",G,d(T(e(i).next_scheduled_at)),1)]),t("div",H,[J,t("p",O,d(e(i).lead.first_name||"-")+" "+d(e(i).lead.last_name||"-"),1)])]),t("div",R,[t("div",W,[X,t("span",Y,d(e(i).sequence.name),1)]),t("div",tt,[et,t("span",st,d(e(i).subject),1)])])])]),t("div",at,[t("div",ot,[t("div",nt,[s(k,{onClick:o[0]||(o[0]=n=>j(a.tag))},{default:_(()=>[w(" Email Tags ")]),_:1})])]),t("form",{onSubmit:o[2]||(o[2]=M(n=>e(r).patch(a.route("sent-email.update",e(i).id)),["prevent"]))},[t("div",it,[t("div",lt,[t("div",ct,[s(q,{value:"Email Content",class:"mb-2"}),s(e(U),{content:e(r).content,"onUpdate:content":o[1]||(o[1]=n=>e(r).content=n),contentType:"html",theme:"snow",toolbar:"essential",class:"bg-white",style:{"min-height":"300px"}},null,8,["content"]),s(V,{class:"mt-2",message:e(r).errors.content},null,8,["message"])])])]),t("div",dt,[t("div",rt,[s(B,{href:a.route("sent-email.index")},{svg:_(()=>[_t]),_:1},8,["href"]),s(k,{disabled:e(r).processing},{default:_(()=>[w("Update")]),_:1},8,["disabled"]),s($,{"enter-active-class":"transition ease-in-out","enter-from-class":"opacity-0","leave-active-class":"transition ease-in-out","leave-to-class":"opacity-0"},{default:_(()=>[e(r).recentlySuccessful?(l(),c("p",mt,"Saved.")):h("",!0)]),_:1})])])],32)])]),s(F,{show:u.value,onClose:v},{default:_(()=>[t("div",ut,[(l(),c("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",class:"h-6 w-6 text-gray-500 absolute top-4 right-4 cursor-pointer hover:text-red-500",onClick:v},xt)),ft,t("div",ht,[t("table",gt,[yt,g.tags.length===0?(l(),c("tr",vt,wt)):h("",!0),t("tbody",kt,[(l(!0),c(b,null,L(g.tags,(n,f)=>(l(),c("tr",{key:f},[t("td",Tt,[t("div",jt,[t("span",null,d(n.name),1),t("span",{onClick:Mt=>C(n.name,f,"name"),class:"cursor-pointer"}," 📋 ",8,Ct)]),p.value===f&&x.value==="name"?(l(),c("span",St," Copied! ")):h("",!0)]),t("td",Et,[t("div",Dt,[t("span",null,d(n.description),1)])])]))),128))])])])])]),_:1},8,["show"])]),_:1})],64))}};export{Qt as default};
