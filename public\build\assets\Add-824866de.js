import{o as l,c as g,a as s,u as a,w as _,F as y,Z as f,b as i,h as c,e as r,f as o,g as V,A as k}from"./app-f4e029a5.js";import{_ as b,b as z}from"./AdminLayout-f619cb63.js";import{_ as d}from"./InputError-00daa197.js";import{_ as m}from"./InputLabel-0f849d64.js";import{P as x}from"./PrimaryButton-faa68167.js";import{_ as u}from"./TextInput-8aed9252.js";import{u as w}from"./index-6192dbda.js";import"./_plugin-vue_export-helper-c27b6911.js";const C={class:"animate-top"},$={class:"bg-white p-4 shadow sm:p-6 sm:rounded-lg border"},U=i("h2",{class:"text-2xl font-semibold leading-7 text-gray-900"},"Add New Leads",-1),L=["onSubmit"],N={class:"border-b border-gray-900/10 pb-12"},S={class:"mt-10 grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-6"},B={class:"sm:col-span-2"},F={class:"sm:col-span-2"},A={class:"sm:col-span-2"},O={class:"sm:col-span-2"},R={class:"sm:col-span-2"},j={class:"sm:col-span-2"},E={class:"sm:col-span-3"},P={class:"sm:col-span-3"},T={class:"sm:col-span-3"},D={class:"sm:col-span-3"},M={class:"flex mt-6 items-center justify-between"},W={class:"ml-auto flex items-center justify-end gap-x-6"},Z=i("button",{type:"button",class:"text-sm font-semibold leading-6 text-gray-900"},"Cancel",-1),q={key:0,class:"text-sm text-gray-600"},ee={__name:"Add",setup(G){const e=w("post","/leads",{first_name:"",last_name:"",country:"",city:"",name:"",designation:"",email:"",organization_name:"",organization_website_url:"",linkedin_url:"",organization_linkedin_url:""}),p=()=>e.submit({preserveScroll:!0,onSuccess:()=>e.reset()});return(v,n)=>(l(),g(y,null,[s(a(f),{title:"Leads"}),s(b,null,{default:_(()=>[i("div",C,[i("div",$,[U,i("form",{onSubmit:c(p,["prevent"]),class:""},[i("div",N,[i("div",S,[i("div",B,[s(m,{for:"first_name",value:"First Name"}),s(u,{id:"first_name",type:"text",modelValue:a(e).first_name,"onUpdate:modelValue":n[0]||(n[0]=t=>a(e).first_name=t),autocomplete:"first_name",onChange:n[1]||(n[1]=t=>a(e).validate("first_name"))},null,8,["modelValue"]),a(e).invalid("first_name")?(l(),r(d,{key:0,class:"",message:a(e).errors.first_name},null,8,["message"])):o("",!0)]),i("div",F,[s(m,{for:"last_name",value:"Last Name"}),s(u,{id:"last_name",type:"text",modelValue:a(e).last_name,"onUpdate:modelValue":n[2]||(n[2]=t=>a(e).last_name=t),autocomplete:"last_name",onChange:n[3]||(n[3]=t=>a(e).validate("last_name"))},null,8,["modelValue"]),a(e).invalid("last_name")?(l(),r(d,{key:0,class:"",message:a(e).errors.last_name},null,8,["message"])):o("",!0)]),i("div",A,[s(m,{for:"country",value:"Country"}),s(u,{id:"country",type:"text",modelValue:a(e).country,"onUpdate:modelValue":n[4]||(n[4]=t=>a(e).country=t),autocomplete:"country",onChange:n[5]||(n[5]=t=>a(e).validate("country"))},null,8,["modelValue"]),a(e).invalid("country")?(l(),r(d,{key:0,class:"",message:a(e).errors.country},null,8,["message"])):o("",!0)]),i("div",O,[s(m,{for:"city",value:"City"}),s(u,{id:"city",type:"text",modelValue:a(e).city,"onUpdate:modelValue":n[6]||(n[6]=t=>a(e).city=t),autocomplete:"city",onChange:n[7]||(n[7]=t=>a(e).validate("city"))},null,8,["modelValue"]),a(e).invalid("city")?(l(),r(d,{key:0,class:"",message:a(e).errors.city},null,8,["message"])):o("",!0)]),i("div",R,[s(m,{for:"designation",value:"Designation"}),s(u,{id:"designation",type:"text",modelValue:a(e).designation,"onUpdate:modelValue":n[8]||(n[8]=t=>a(e).designation=t),autocomplete:"designation",onChange:n[9]||(n[9]=t=>a(e).validate("designation"))},null,8,["modelValue"]),a(e).invalid("designation")?(l(),r(d,{key:0,class:"",message:a(e).errors.designation},null,8,["message"])):o("",!0)]),i("div",j,[s(m,{for:"email",value:"Email"}),s(u,{id:"email",type:"email",modelValue:a(e).email,"onUpdate:modelValue":n[10]||(n[10]=t=>a(e).email=t),autocomplete:"email",onChange:n[11]||(n[11]=t=>a(e).validate("email"))},null,8,["modelValue"]),a(e).invalid("email")?(l(),r(d,{key:0,class:"",message:a(e).errors.email},null,8,["message"])):o("",!0)]),i("div",E,[s(m,{for:"organization_name",value:"Organization Name"}),s(u,{id:"organization_name",type:"text",modelValue:a(e).organization_name,"onUpdate:modelValue":n[12]||(n[12]=t=>a(e).organization_name=t),autocomplete:"organization_name",onChange:n[13]||(n[13]=t=>a(e).validate("organization_name"))},null,8,["modelValue"]),a(e).invalid("organization_name")?(l(),r(d,{key:0,class:"",message:a(e).errors.organization_name},null,8,["message"])):o("",!0)]),i("div",P,[s(m,{for:"organization_website_url",value:"Organization Website URL"}),s(u,{id:"organization_website_url",type:"url",modelValue:a(e).organization_website_url,"onUpdate:modelValue":n[14]||(n[14]=t=>a(e).organization_website_url=t),onChange:n[15]||(n[15]=t=>a(e).validate("organization_website_url"))},null,8,["modelValue"]),a(e).invalid("organization_website_url")?(l(),r(d,{key:0,message:a(e).errors.organization_website_url},null,8,["message"])):o("",!0)]),i("div",T,[s(m,{for:"linkedin_url",value:"Linkedin URL"}),s(u,{id:"linkedin_url",type:"url",modelValue:a(e).linkedin_url,"onUpdate:modelValue":n[16]||(n[16]=t=>a(e).linkedin_url=t),onChange:n[17]||(n[17]=t=>a(e).validate("linkedin_url"))},null,8,["modelValue"]),a(e).invalid("linkedin_url")?(l(),r(d,{key:0,message:a(e).errors.linkedin_url},null,8,["message"])):o("",!0)]),i("div",D,[s(m,{for:"organization_linkedin_url",value:"Organization Linkedin URL"}),s(u,{id:"organization_linkedin_url",type:"url",modelValue:a(e).organization_linkedin_url,"onUpdate:modelValue":n[18]||(n[18]=t=>a(e).organization_linkedin_url=t),onChange:n[19]||(n[19]=t=>a(e).validate("organization_linkedin_url"))},null,8,["modelValue"]),a(e).invalid("organization_linkedin_url")?(l(),r(d,{key:0,message:a(e).errors.organization_linkedin_url},null,8,["message"])):o("",!0)])])]),i("div",M,[i("div",W,[s(z,{href:v.route("leads.index")},{svg:_(()=>[Z]),_:1},8,["href"]),s(x,{disabled:a(e).processing},{default:_(()=>[V("Save")]),_:1},8,["disabled"]),s(k,{"enter-active-class":"transition ease-in-out","enter-from-class":"opacity-0","leave-active-class":"transition ease-in-out","leave-to-class":"opacity-0"},{default:_(()=>[a(e).recentlySuccessful?(l(),g("p",q,"Saved.")):o("",!0)]),_:1})])])],40,L)])])]),_:1})],64))}};export{ee as default};
