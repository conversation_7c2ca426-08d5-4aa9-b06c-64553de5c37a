<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Carbon\Carbon;

class Prospect extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'first_name',
        'last_name',
        'email',
        'phone',
        'company',
        'position',
        'country',
        'city',
        'lead_source',
        'lead_source_details',
        'linkedin_url',
        'website_url',
        'company_website',
        'status',
        'priority',
        'score',
        'initial_conversation',
        'notes',
        'conversation_history',
        'last_contacted_at',
        'next_follow_up_at',
        'estimated_budget',
        'project_type',
        'requirements',
        'budget_range',
        'converted_lead_id',
        'converted_at',
        'assigned_to',
    ];

    protected $casts = [
        'conversation_history' => 'array',
        'last_contacted_at' => 'date',
        'next_follow_up_at' => 'date',
        'converted_at' => 'date',
        'estimated_budget' => 'decimal:2',
    ];

    // Relationships
    public function convertedLead()
    {
        return $this->belongsTo(Leads::class, 'converted_lead_id');
    }

    public function assignedUser()
    {
        return $this->belongsTo(User::class, 'assigned_to');
    }

    public function activities()
    {
        return $this->hasMany(ProspectActivity::class)->orderBy('created_at', 'desc');
    }

    // Scopes
    public function scopeByStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    public function scopeByPriority($query, $priority)
    {
        return $query->where('priority', $priority);
    }

    public function scopeByLeadSource($query, $source)
    {
        return $query->where('lead_source', $source);
    }

    public function scopeNeedFollowUp($query)
    {
        return $query->whereNotNull('next_follow_up_at')
                    ->where('next_follow_up_at', '<=', Carbon::today());
    }

    public function scopeAssignedTo($query, $userId)
    {
        return $query->where('assigned_to', $userId);
    }

    // Accessors
    public function getFullNameAttribute()
    {
        return $this->first_name . ' ' . $this->last_name;
    }

    public function getIsOverdueAttribute()
    {
        return $this->next_follow_up_at && $this->next_follow_up_at->isPast();
    }

    public function getStatusBadgeColorAttribute()
    {
        return match($this->status) {
            'new' => 'blue',
            'contacted' => 'yellow',
            'qualified' => 'green',
            'unqualified' => 'red',
            'converted' => 'purple',
            'lost' => 'gray',
            default => 'gray'
        };
    }

    public function getPriorityBadgeColorAttribute()
    {
        return match($this->priority) {
            'low' => 'gray',
            'medium' => 'blue',
            'high' => 'orange',
            'urgent' => 'red',
            default => 'gray'
        };
    }

    // Methods
    public function addActivity($type, $title, $description = null, $metadata = null, $userId = null, $activityDate = null)
    {
        return $this->activities()->create([
            'user_id' => $userId ?? auth()->id(),
            'activity_type' => $type,
            'title' => $title,
            'description' => $description,
            'metadata' => $metadata,
            'activity_date' => $activityDate ? Carbon::parse($activityDate) : Carbon::today(),
        ]);
    }

    public function updateStatus($newStatus, $notes = null)
    {
        $oldStatus = $this->status;

        if($newStatus == 'lost'){
            $this->update(['status' => $newStatus, 'next_follow_up_at' => null]);
        } else {
            $this->update(['status' => $newStatus]);
        }

        $this->addActivity(
            'status_changed',
            "Status changed from {$oldStatus} to {$newStatus}",
            $notes
        );
    }

    public function convertToLead($leadData = [])
    {
        if ($this->status === 'converted') {
            return $this->convertedLead;
        }

        // Prepare lead data from prospect
        $defaultLeadData = [
            'first_name' => $this->first_name,
            'last_name' => $this->last_name,
            'email' => $this->email,
            'country' => $this->country,
            'city' => $this->city,
            'designation' => $this->position,
            'organization_name' => $this->company,
            'organization_website_url' => $this->company_website,
            'linkedin_url' => $this->linkedin_url,
            'organization_linkedin_url' => $this->company_website,
        ];

        $leadData = array_merge($defaultLeadData, $leadData);

        $lead = Leads::create($leadData);

        $this->update([
            'status' => 'converted',
            'converted_lead_id' => $lead->id,
            'converted_at' => Carbon::today(),
        ]);

        $this->addActivity(
            'status_changed',
            'Prospect converted to lead',
            "Lead ID: {$lead->id}"
        );

        return $lead;
    }
}
