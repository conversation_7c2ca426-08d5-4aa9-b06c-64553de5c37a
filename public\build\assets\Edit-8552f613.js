import{_ as o}from"./AdminLayout-f619cb63.js";import i from"./DeleteUserForm-68a95269.js";import m from"./UpdatePasswordForm-5fe72b4b.js";import r from"./UpdateProfileInformationForm-4d21612f.js";import{o as l,c,a as t,u as n,w as e,F as p,Z as d,b as s}from"./app-f4e029a5.js";import"./DangerButton-d8229525.js";import"./_plugin-vue_export-helper-c27b6911.js";import"./InputError-00daa197.js";import"./InputLabel-0f849d64.js";import"./Modal-22d517c7.js";/* empty css                                                              */import"./SecondaryButton-7ac28605.js";import"./TextInput-8aed9252.js";import"./PrimaryButton-faa68167.js";import"./TextArea-c0956229.js";const _=s("h2",{class:"font-semibold text-xl text-gray-800 leading-tight"},"Profile",-1),u={class:""},f={class:"max-w-7xl mx-auto sm:px-6 lg:px-8 space-y-6"},h={class:"p-4 sm:p-8 bg-white shadow sm:rounded-lg"},x={class:"p-4 sm:p-8 bg-white shadow sm:rounded-lg"},g={class:"p-4 sm:p-8 bg-white shadow sm:rounded-lg"},z={__name:"Edit",props:{mustVerifyEmail:{type:Boolean},status:{type:String}},setup(a){return(w,y)=>(l(),c(p,null,[t(n(d),{title:"Profile"}),t(o,null,{header:e(()=>[_]),default:e(()=>[s("div",u,[s("div",f,[s("div",h,[t(r,{"must-verify-email":a.mustVerifyEmail,status:a.status,class:"max-w-xl"},null,8,["must-verify-email","status"])]),s("div",x,[t(m,{class:"max-w-xl"})]),s("div",g,[t(i,{class:"max-w-xl"})])])])]),_:1})],64))}};export{z as default};
