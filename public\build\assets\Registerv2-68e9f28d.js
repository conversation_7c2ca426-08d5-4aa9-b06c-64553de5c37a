import{l as n,o as c,e as _,w as r,a as i,u as d,Z as l,b as e,p as u,m as p}from"./app-f4e029a5.js";import{_ as h}from"./_plugin-vue_export-helper-c27b6911.js";const o=t=>(u("data-v-5d477e3c"),t=t(),p(),t),v={class:"container"},m=o(()=>e("h1",null,"403",-1)),f=o(()=>e("h2",null,"Access Denied",-1)),g=o(()=>e("p",null,"You do not have permission to access this page/event. ",-1)),y=o(()=>e("span",null,"Please contact your administrator if you believe this is an error.",-1)),R=["href"],x={__name:"Registerv2",setup(t){return(s,I)=>{const a=n("GuestLayout");return c(),_(a,null,{default:r(()=>[i(d(l),{title:"Register"}),e("div",v,[m,f,g,y,e("h4",null,[e("a",{href:s.route("login")},"Return to Login",8,R)])])]),_:1})}}},L=h(x,[["__scopeId","data-v-5d477e3c"]]);export{L as default};
