import{o,e as s,w as i,s as r,u as n,j as l}from"./app-f4e029a5.js";const f={__name:"Create<PERSON>utt<PERSON>",props:{href:{type:String,required:!0},active:{type:Boolean}},setup(e){return(t,u)=>(o(),s(n(l),{href:e.href,class:"flex w-full justify-center rounded-md bg-indigo-600 px-6 py-1.5 text-sm font-semibold leading-6 text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600"},{default:i(()=>[r(t.$slots,"default")]),_:3},8,["href"]))}};export{f as _};
