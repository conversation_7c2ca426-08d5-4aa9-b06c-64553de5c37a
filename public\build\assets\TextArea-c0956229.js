import{r as u,i as l,o as n,c as i}from"./app-f4e029a5.js";const m=["value"],d={__name:"TextArea",props:{modelValue:{type:String}},emits:["update:modelValue"],setup(a,{expose:o}){const e=u(null);return l(()=>{e.value.hasAttribute("autofocus")&&e.value.focus()}),o({focus:()=>e.value.focus()}),(r,t)=>(n(),i("textarea",{class:"mt-2 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6",value:a.modelValue,onInput:t[0]||(t[0]=s=>r.$emit("update:modelValue",s.target.value)),ref_key:"textarea",ref:e},null,40,m))}};export{d as _};
